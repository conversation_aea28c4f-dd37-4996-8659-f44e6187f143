import cv2
import time
import threading
from collections import defaultdict
from ultralytics import YOLO
import sqlite3
import os
from datetime import datetime

class CameraMonitor:
    def __init__(self, model_path="best.pt"):
        """Initialize the camera monitoring system"""
        self.model_path = model_path
        self.model = None
        self.camera = None
        self.is_running = False
        self.current_frame = None
        self.detected_products = defaultdict(int)
        self.scanned_products = defaultdict(int)
        self.alarm_messages = []
        self.frame_lock = threading.Lock()
        self.alarm_lock = threading.Lock()
        
        # Load YOLO model
        try:
            self.model = YOLO(model_path)
            print(f"✅ YOLO model loaded: {model_path}")
        except Exception as e:
            print(f"❌ Error loading YOLO model: {e}")
            # Fallback to default model
            try:
                self.model = YOLO("yolov8n.pt")
                print("✅ Using default YOLOv8n model")
            except Exception as e2:
                print(f"❌ Error loading default model: {e2}")
                self.model = None

    def get_db_connection(self):
        """Get database connection"""
        db_path = os.environ.get('DATABASE_PATH', 'products.db')
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        return conn

    def get_product_by_name(self, product_name):
        """Get product information from database by name"""
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM products WHERE name LIKE ?', (f'%{product_name}%',))
            product = cursor.fetchone()
            conn.close()
            return dict(product) if product else None
        except Exception as e:
            print(f"Database error: {e}")
            return None

    def start_camera(self):
        """Start the camera system"""
        print("🚀 Starting camera system...")

        # First, make sure any existing camera is properly stopped
        if self.camera is not None:
            print("⚠️ Camera already exists, stopping first...")
            self.stop_camera()
            time.sleep(2)  # Give extra time for cleanup

        try:
            # Try to use Picamera2 first (for Raspberry Pi)
            try:
                from picamera2 import Picamera2
                print("📷 Initializing Picamera2...")
                self.camera = Picamera2()
                self.camera.preview_configuration.main.size = (640, 480)
                self.camera.preview_configuration.main.format = "RGB888"
                self.camera.configure("preview")
                self.camera.start()
                time.sleep(2)
                self.camera_type = "picamera2"
                print("✅ Picamera2 initialized successfully")
            except ImportError:
                print("📷 Picamera2 not available, trying webcam...")
                # Fallback to regular webcam
                self.camera = cv2.VideoCapture(0)
                if not self.camera.isOpened():
                    raise Exception("Could not open webcam")
                self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
                self.camera_type = "webcam"
                print("✅ Webcam initialized successfully")
            except Exception as picam_error:
                print(f"⚠️ Picamera2 failed: {picam_error}, trying webcam...")
                # Fallback to regular webcam
                self.camera = cv2.VideoCapture(0)
                if not self.camera.isOpened():
                    raise Exception("Could not open webcam")
                self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
                self.camera_type = "webcam"
                print("✅ Webcam initialized successfully")

            # Clear previous data
            self.detected_products.clear()
            self.scanned_products.clear()
            self.alarm_messages.clear()

            self.is_running = True
            print("🎉 Camera system started successfully!")
            return True

        except Exception as e:
            print(f"❌ Error starting camera: {e}")
            self.camera = None
            self.camera_type = None
            self.is_running = False
            return False

    def stop_camera(self):
        """Stop the camera system"""
        print("🛑 Stopping camera...")
        self.is_running = False

        if self.camera:
            try:
                if self.camera_type == "picamera2":
                    print("📷 Stopping Picamera2...")
                    self.camera.stop()
                    self.camera.close()  # Properly close Picamera2
                else:
                    print("📷 Releasing webcam...")
                    self.camera.release()

                # Give time for camera to properly release
                import time
                time.sleep(1)

            except Exception as e:
                print(f"⚠️ Error stopping camera: {e}")
            finally:
                self.camera = None
                self.camera_type = None

        # Clear detection data
        self.detected_products.clear()
        self.scanned_products.clear()
        self.current_frame = None

        print("✅ Camera stopped and resources released")

    def capture_frame(self):
        """Capture a frame from camera"""
        if not self.camera or not self.is_running:
            return None
            
        try:
            if self.camera_type == "picamera2":
                frame = self.camera.capture_array()
            else:
                ret, frame = self.camera.read()
                if not ret:
                    return None
            return frame
        except Exception as e:
            print(f"Error capturing frame: {e}")
            return None

    def detect_products(self, frame):
        """Detect products in frame using YOLO"""
        if self.model is None:
            return frame, {}

        try:
            # Lower confidence threshold for better detection
            results = self.model(frame, imgsz=640, conf=0.3)
            annotated_frame = results[0].plot()

            # Extract detected products
            detected_counts = defaultdict(int)
            if len(results[0].boxes) > 0:
                names = results[0].names
                class_ids = results[0].boxes.cls.cpu().numpy()
                confidences = results[0].boxes.conf.cpu().numpy()

                print(f"🔍 YOLO Detection Debug:")
                for i, class_id in enumerate(class_ids):
                    product_name = names[int(class_id)]
                    confidence = confidences[i]
                    print(f"  - Detected: {product_name} (confidence: {confidence:.2f})")
                    detected_counts[product_name] += 1

                print(f"📊 Final detected counts: {dict(detected_counts)}")
            else:
                print("🔍 No objects detected by YOLO")

            return annotated_frame, detected_counts
        except Exception as e:
            print(f"❌ Error in detection: {e}")
            return frame, {}

    def add_alarm(self, message, alarm_type="warning"):
        """Add alarm message"""
        with self.alarm_lock:
            alarm = {
                'message': message,
                'type': alarm_type,
                'timestamp': datetime.now(),
                'id': len(self.alarm_messages)
            }
            self.alarm_messages.append(alarm)
            # Keep only last 50 alarms
            if len(self.alarm_messages) > 50:
                self.alarm_messages = self.alarm_messages[-50:]

    def get_recent_alarms(self, limit=10):
        """Get recent alarm messages"""
        with self.alarm_lock:
            return self.alarm_messages[-limit:] if self.alarm_messages else []

    def check_alarms(self, detected_counts):
        """Check for alarm conditions"""
        # Check for unscanned products
        for product, qty in detected_counts.items():
            if self.scanned_products[product] == 0:
                self.add_alarm(f"Unscanned product detected: {product}", "warning")
            elif qty > self.scanned_products[product]:
                self.add_alarm(f"Extra {product}: scanned {self.scanned_products[product]}, seen {qty}", "warning")

        # Check for removed products
        for product in self.scanned_products:
            if self.scanned_products[product] > 0 and product not in detected_counts:
                self.add_alarm(f"Product {product} removed from cart", "warning")

    def add_scanned_product(self, product_name, quantity=1):
        """Add a scanned product (simulating barcode scan)"""
        self.scanned_products[product_name] += quantity
        self.add_alarm(f"Added {product_name} to cart", "info")

    def get_frame_data(self):
        """Get current frame and detection data"""
        with self.frame_lock:
            return {
                'detected_products': dict(self.detected_products),
                'scanned_products': dict(self.scanned_products),
                'alarms': self.get_recent_alarms()
            }

    def process_frame(self):
        """Process a single frame - main detection loop"""
        print("🔄 Processing frame...")

        frame = self.capture_frame()
        if frame is None:
            print("❌ No frame captured")
            return None

        print(f"✅ Frame captured: {frame.shape}")

        # Detect products
        annotated_frame, detected_counts = self.detect_products(frame)

        print(f"🔍 Detection result: {detected_counts}")

        # Update detected products
        with self.frame_lock:
            self.detected_products = detected_counts
            self.current_frame = annotated_frame

        # Check for alarms
        self.check_alarms(detected_counts)

        return annotated_frame

# Global camera monitor instance
camera_monitor = CameraMonitor()
