
from picamera2 import Picamera2
import cv2
from ultralytics import YOLO
import time
from collections import defaultdict
import numpy as np

# تحميل النموذج
model = YOLO("best.pt")  # غيّر الاسم حسب اسم نموذجك

# تهيئة الكاميرا
picam2 = Picamera2()
picam2.preview_configuration.main.size = (640, 480)
picam2.preview_configuration.main.format = "RGB888"
picam2.configure("preview")
picam2.start()
time.sleep(2)

# إعدادات العرض
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600
ALARM_DISPLAY_TIME = 3  # seconds to show alarm
alarm_messages = []
alarm_start_time = time.time()

# المنتجات الممسوحة يدويًا (محاكاة بالسكان)
scanned_products = defaultdict(int)

def add_alarm_message(message, alarm_type="warning"):
    """إضافة رسالة تنبيه للعرض على الشاشة"""
    global alarm_messages, alarm_start_time
    alarm_messages.append({
        'message': message,
        'type': alarm_type,
        'timestamp': time.time()
    })
    alarm_start_time = time.time()

def draw_alarm_overlay(frame):
    """رسم رسائل التنبيه على الإطار"""
    global alarm_messages
    current_time = time.time()

    # تنظيف الرسائل القديمة
    alarm_messages = [msg for msg in alarm_messages
                     if current_time - msg['timestamp'] < ALARM_DISPLAY_TIME]

    if not alarm_messages:
        return frame

    # إنشاء overlay شفاف للتنبيهات
    overlay = frame.copy()

    # رسم خلفية للتنبيهات
    cv2.rectangle(overlay, (10, 10), (frame.shape[1] - 10, 150), (0, 0, 0), -1)
    cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)

    # عرض رسائل التنبيه
    y_offset = 40
    for i, alarm in enumerate(alarm_messages[-3:]):  # عرض آخر 3 تنبيهات فقط
        color = (0, 0, 255) if alarm['type'] == 'warning' else (0, 255, 255)
        cv2.putText(frame, f"⚠️ {alarm['message']}", (20, y_offset + i * 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

    return frame

def draw_info_panel(frame, detected_counts, scanned_products):
    """رسم لوحة المعلومات على الجانب"""
    info_width = 250
    info_height = frame.shape[0]

    # إنشاء لوحة معلومات
    info_panel = np.zeros((info_height, info_width, 3), dtype=np.uint8)
    info_panel[:] = (40, 40, 40)  # خلفية رمادية داكنة

    # عنوان اللوحة
    cv2.putText(info_panel, "Smart Cart Monitor", (10, 30),
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

    # خط فاصل
    cv2.line(info_panel, (10, 40), (info_width - 10, 40), (255, 255, 255), 1)

    # المنتجات الممسوحة
    y_pos = 70
    cv2.putText(info_panel, "Scanned Products:", (10, y_pos),
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    y_pos += 25

    for product, qty in scanned_products.items():
        if qty > 0:
            text = f"- {product}: {qty}"
            cv2.putText(info_panel, text, (15, y_pos),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
            y_pos += 20

    # المنتجات المكتشفة
    y_pos += 20
    cv2.putText(info_panel, "Camera Detected:", (10, y_pos),
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
    y_pos += 25

    for product, qty in detected_counts.items():
        text = f"- {product}: {qty}"
        cv2.putText(info_panel, text, (15, y_pos),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        y_pos += 20

    # دمج اللوحة مع الإطار الأصلي
    combined_frame = np.hstack([frame, info_panel])
    return combined_frame

print("🔍 Smart Cart Camera System Started")
print("Press 'S' to add product manually, 'Q' to quit")

try:
    # إنشاء نافذة بحجم مخصص
    cv2.namedWindow("Smart Cart Camera System", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("Smart Cart Camera System", WINDOW_WIDTH + 250, WINDOW_HEIGHT)

    while True:
        frame = picam2.capture_array()
        results = model(frame, imgsz=640, conf=0.7)
        annotated_frame = results[0].plot()

        names = results[0].names
        class_ids = results[0].boxes.cls.cpu().numpy()

        # حساب المنتجات اللي الكاميرا شايفاها الآن
        detected_counts = defaultdict(int)
        for class_id in class_ids:
            product_name = names[int(class_id)]
            detected_counts[product_name] += 1

        # فحص التنبيهات وإضافتها للعرض المرئي
        for product, qty in detected_counts.items():
            if scanned_products[product] == 0:
                add_alarm_message(f"Unscanned product detected: {product}", "warning")
            elif qty > scanned_products[product]:
                add_alarm_message(f"Extra {product}: scanned {scanned_products[product]}, seen {qty}", "warning")

        for product in scanned_products:
            if scanned_products[product] > 0 and product not in detected_counts:
                add_alarm_message(f"Product {product} removed from cart", "warning")

        # تطبيق التأثيرات المرئية
        annotated_frame = draw_alarm_overlay(annotated_frame)
        final_frame = draw_info_panel(annotated_frame, detected_counts, scanned_products)

        # عرض الإطار النهائي
        cv2.imshow("Smart Cart Camera System", final_frame)

        key = cv2.waitKey(1) & 0xFF
        if key == ord("q"):
            break
        elif key == ord("s"):
            # إضافة منتج يدويًا (محاكاة للسكان)
            print("Enter product name in terminal...")
            # استخدام input في terminal مؤقتاً
            product = input("🔠 Enter product name (as it appears in YOLO class): ").strip()
            if product:
                scanned_products[product] += 1
                add_alarm_message(f"Added {product} to cart", "info")
                print(f"✅ Added {product} to scanned list.")

except KeyboardInterrupt:
    print("\n🛑 Smart Cart System stopped manually.")

except Exception as e:
    print(f"\n❌ Error occurred: {e}")
    add_alarm_message(f"System Error: {str(e)}", "error")

finally:
    print("\n🔄 Cleaning up...")
    picam2.stop()
    cv2.destroyAllWindows()
    print("✅ Smart Cart Camera System closed successfully.")
