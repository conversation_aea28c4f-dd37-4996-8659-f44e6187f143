import sqlite3
import os

# إنشاء ملف قاعدة البيانات في المجلد الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
DB_PATH = os.path.join(current_dir, 'products.db')

print(f"Creating database at: {DB_PATH}")  # Debug print

def create_tables():
    try:
        # التأكد من وجود المجلد
        os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # إنشاء جدول المنتجات
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            barcode TEXT UNIQUE NOT NULL,
            price REAL NOT NULL,
            image_path TEXT,
            stock INTEGER DEFAULT 0
        )
        ''')
        
        # إنشاء جدول الفواتير
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            total_amount REAL NOT NULL,
            payment_method TEXT NOT NULL
        )
        ''')
        
        # إنشاء جدول عناصر الفاتورة
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS order_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            product_name TEXT NOT NULL,
            quantity INTEGER NOT NULL,
            price REAL NOT NULL,
            FOREIGN KEY (order_id) REFERENCES orders (id),
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
        ''')
        
        conn.commit()
        conn.close()
        print("✅ تم إنشاء الجداول بنجاح!")
        print(f"✅ Database created successfully at: {DB_PATH}")
        
    except Exception as e:
        print(f"❌ Error creating database: {str(e)}")
        print(f"Current working directory: {os.getcwd()}")
        print(f"Attempted database path: {DB_PATH}")
        raise

if __name__ == "__main__":
    create_tables()
