from flask import Blueprint, render_template, request, redirect, url_for, session, flash, jsonify, Response
import sqlite3
import os
from werkzeug.utils import secure_filename
from functools import wraps
import cv2
import time
import threading
from camera_monitor import camera_monitor

admin_bp = Blueprint('admin', __name__, url_prefix='/admin', template_folder=os.path.join(os.path.dirname(__file__), 'templates'))

# Static user credentials (in a real project, these would be in the database)
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "1234"

# Login verification function
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'admin_logged_in' not in session or not session['admin_logged_in']:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('admin.login'))
        return f(*args, **kwargs)
    return decorated_function

UPLOAD_FOLDER = 'static/images'
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

# Database connection function
def get_db_connection():
    db_path = os.environ.get('DATABASE_PATH')
    if not db_path:
        # Use relative path if environment variable is not set
        db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'products.db')
    print(f"Connecting to database at: {db_path}")  # Debug print
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    return conn

@admin_bp.route('/login', methods=['GET', 'POST'])
def login():
    error = None
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        if username == ADMIN_USERNAME and password == ADMIN_PASSWORD:
            session['admin_logged_in'] = True
            flash('Welcome back, admin!', 'success')
            return redirect(url_for('admin.show_product'))
        else:
            error = 'Invalid username or password. Please try again.'
    
    return render_template('admin_login.html', error=error)

@admin_bp.route('/logout')
def logout():
    session.pop('admin_logged_in', None)
    flash('You have been logged out successfully.', 'info')
    return redirect(url_for('home'))

@admin_bp.route('/products')
@login_required
def show_product():
    search_query = request.args.get('search', '')
    conn = get_db_connection()
    cursor = conn.cursor()

    if search_query:
        cursor.execute(''' 
            SELECT * FROM products 
            WHERE name LIKE ? OR barcode LIKE ? 
        ''', ('%' + search_query + '%', '%' + search_query + '%'))
    else:
        cursor.execute('SELECT * FROM products')

    products = cursor.fetchall()
    conn.close()

    return render_template('show_product.html', products=products)

@admin_bp.route('/add_product', methods=['GET', 'POST'])
@login_required
def add_product():
    if request.method == 'POST':
        try:
            name = request.form['name']
            barcode = request.form['barcode']
            price = float(request.form['price'])
            stock = int(request.form['stock'])
            image = request.files['image']

            image_filename = ''
            if image and image.filename != '':
                filename = secure_filename(image.filename)
                image_filename = os.path.join(UPLOAD_FOLDER, filename)
                image.save(image_filename)

            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute('INSERT INTO products (name, barcode, price, stock, image_path) VALUES (?, ?, ?, ?, ?)',
                           (name, barcode, price, stock, image_filename))
            conn.commit()
            conn.close()
            flash('Product added successfully!', 'success')
            return redirect(url_for('admin.show_product'))
        except Exception as e:
            flash(f'Error adding product: {str(e)}', 'danger')
            return redirect(url_for('admin.add_product'))
    
    return render_template('add_product.html')

@admin_bp.route('/delete_product/<int:product_id>')
@login_required
def delete_product(product_id):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get product details before deletion
        cursor.execute("SELECT name FROM products WHERE id=?", (product_id,))
        product = cursor.fetchone()
        
        cursor.execute("DELETE FROM products WHERE id=?", (product_id,))
        conn.commit()
        conn.close()
        
        if product:
            flash(f'Product "{product["name"]}" has been deleted successfully.', 'success')
        else:
            flash('Product not found.', 'warning')
            
    except Exception as e:
        flash(f'Error deleting product: {str(e)}', 'danger')
    
    return redirect(url_for('admin.show_product'))

@admin_bp.route('/edit_product/<int:product_id>', methods=['GET', 'POST'])
@login_required
def edit_product(product_id):
    conn = get_db_connection()
    cursor = conn.cursor()

    if request.method == 'POST':
        try:
            name = request.form['name']
            barcode = request.form['barcode']
            price = float(request.form['price'])
            stock = int(request.form['stock'])
            image = request.files['image']

            # Get current image path
            cursor.execute("SELECT image_path FROM products WHERE id=?", (product_id,))
            current_image = cursor.fetchone()['image_path']

            image_path = current_image
            if image and image.filename != '':
                filename = secure_filename(image.filename)
                image_path = os.path.join(UPLOAD_FOLDER, filename)
                image.save(image_path)

            cursor.execute(''' 
                UPDATE products
                SET name=?, barcode=?, price=?, stock=?, image_path=?
                WHERE id=? 
            ''', (name, barcode, price, stock, image_path, product_id))
            conn.commit()
            flash('Product updated successfully!', 'success')
            return redirect(url_for('admin.show_product'))
        except Exception as e:
            flash(f'Error updating product: {str(e)}', 'danger')
            return redirect(url_for('admin.edit_product', product_id=product_id))
        finally:
            conn.close()

    cursor.execute("SELECT * FROM products WHERE id=?", (product_id,))
    product = cursor.fetchone()
    
    if not product:
        flash('Product not found.', 'warning')
        return redirect(url_for('admin.show_product'))
    
    conn.close()
    return render_template('edit_product.html', product=product)

@admin_bp.route('/orders')
@login_required
def view_orders():
    page = int(request.args.get('page', 1))
    per_page = 10
    offset = (page - 1) * per_page
    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        # Fetch total count for pagination
        cursor.execute('SELECT COUNT(*) FROM orders')
        total_orders = cursor.fetchone()[0]
        total_pages = (total_orders + per_page - 1) // per_page
        # Fetch paginated orders
        cursor.execute('''
            SELECT id, order_date, total_amount
            FROM orders 
            ORDER BY order_date ASC
            LIMIT ? OFFSET ?
        ''', (per_page, offset))
        orders = []
        order_rows = cursor.fetchall()
        for order_row in order_rows:
            order_dict = dict(order_row)
            # Get order items with product details
            cursor.execute('''
                SELECT oi.quantity, p.name as product_name, p.price as product_price, p.image_path
                FROM order_items oi
                JOIN products p ON oi.product_id = p.id
                WHERE oi.order_id = ?
            ''', (order_dict['id'],))
            items = []
            total_items = 0
            total_amount = 0
            item_rows = cursor.fetchall()
            for item_row in item_rows:
                item_dict = dict(item_row)
                total_items += item_dict['quantity']
                total_amount += item_dict['quantity'] * item_dict['product_price']
                items.append(item_dict)
            order_dict['items'] = items
            order_dict['total_items'] = total_items
            order_dict['total_amount'] = total_amount
            orders.append(order_dict)
        return render_template('orders.html', orders=orders, page=page, total_pages=total_pages)
    except Exception as e:
        flash(f'Error loading orders: {str(e)}', 'danger')
        return redirect(url_for('admin.show_product'))
    finally:
        conn.close()

# AJAX endpoint to delete order
@admin_bp.route('/api/delete_order/<int:order_id>', methods=['POST'])
@login_required
def api_delete_order(order_id):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("DELETE FROM order_items WHERE order_id=?", (order_id,))
        cursor.execute("DELETE FROM orders WHERE id=?", (order_id,))
        conn.commit()
        conn.close()
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# AJAX endpoint to get order details
@admin_bp.route('/api/order_details/<int:order_id>')
@login_required
def api_order_details(order_id):
    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        cursor.execute('SELECT id, order_date, total_amount FROM orders WHERE id=?', (order_id,))
        order_row = cursor.fetchone()
        if not order_row:
            return jsonify({'success': False, 'error': 'Order not found'})
        order_dict = dict(order_row)
        cursor.execute('''
            SELECT oi.quantity, p.name as product_name, p.price as product_price, p.image_path
            FROM order_items oi
            JOIN products p ON oi.product_id = p.id
            WHERE oi.order_id = ?
        ''', (order_id,))
        items = [dict(item_row) for item_row in cursor.fetchall()]
        order_dict['items'] = items
        order_dict['total_items'] = sum(item['quantity'] for item in items)
        order_dict['total_amount'] = sum(item['quantity'] * item['product_price'] for item in items)
        return jsonify({'success': True, 'order': order_dict})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})
    finally:
        conn.close()

# ==================== CAMERA MONITORING ROUTES ====================

@admin_bp.route('/security-monitor')
@login_required
def security_monitor():
    """Security monitoring dashboard"""
    return render_template('security_monitor.html')

@admin_bp.route('/camera/start')
@login_required
def start_camera():
    """Start the camera monitoring system"""
    try:
        if camera_monitor.start_camera():
            flash('Camera system started successfully!', 'success')
        else:
            flash('Failed to start camera system. Please check camera connection.', 'danger')
    except Exception as e:
        flash(f'Error starting camera: {str(e)}', 'danger')
    return redirect(url_for('admin.security_monitor'))

@admin_bp.route('/camera/stop')
@login_required
def stop_camera():
    """Stop the camera monitoring system"""
    try:
        camera_monitor.stop_camera()
        flash('Camera system stopped.', 'info')
    except Exception as e:
        flash(f'Error stopping camera: {str(e)}', 'danger')
    return redirect(url_for('admin.security_monitor'))

def generate_frames():
    """Generate video frames for streaming"""
    while camera_monitor.is_running:
        try:
            frame = camera_monitor.process_frame()
            if frame is not None:
                # Encode frame as JPEG
                ret, buffer = cv2.imencode('.jpg', frame)
                if ret:
                    frame_bytes = buffer.tobytes()
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
            time.sleep(0.1)  # Control frame rate
        except Exception as e:
            print(f"Error generating frame: {e}")
            break

@admin_bp.route('/camera/feed')
@login_required
def camera_feed():
    """Video streaming route"""
    return Response(generate_frames(),
                   mimetype='multipart/x-mixed-replace; boundary=frame')

@admin_bp.route('/api/camera/status')
@login_required
def camera_status():
    """Get camera system status"""
    return jsonify({
        'is_running': camera_monitor.is_running,
        'has_camera': camera_monitor.camera is not None,
        'model_loaded': camera_monitor.model is not None
    })

@admin_bp.route('/api/camera/data')
@login_required
def camera_data():
    """Get current detection data and alarms"""
    data = camera_monitor.get_frame_data()
    # Format alarms for JSON
    formatted_alarms = []
    for alarm in data['alarms']:
        formatted_alarms.append({
            'id': alarm['id'],
            'message': alarm['message'],
            'type': alarm['type'],
            'timestamp': alarm['timestamp'].strftime('%H:%M:%S')
        })

    return jsonify({
        'detected_products': data['detected_products'],
        'scanned_products': data['scanned_products'],
        'alarms': formatted_alarms
    })

@admin_bp.route('/api/camera/add-product', methods=['POST'])
@login_required
def add_scanned_product():
    """Manually add a scanned product (for testing)"""
    try:
        data = request.get_json()
        product_name = data.get('product_name', '').strip()
        quantity = int(data.get('quantity', 1))

        if product_name:
            camera_monitor.add_scanned_product(product_name, quantity)
            return jsonify({'success': True, 'message': f'Added {product_name} to scanned products'})
        else:
            return jsonify({'success': False, 'message': 'Product name is required'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})
