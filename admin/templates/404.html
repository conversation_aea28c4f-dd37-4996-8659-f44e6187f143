{% extends "base.html" %}

{% block title %}Page Not Found{% endblock %}

{% block extra_css %}
<style>
    .error-container {
        text-align: center;
        padding: 4rem 1rem;
        max-width: 600px;
        margin: 0 auto;
    }
    
    .error-icon {
        font-size: 5rem;
        color: var(--text-muted);
        margin-bottom: 2rem;
    }
    
    .error-title {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }
    
    .error-message {
        color: var(--text-muted);
        margin-bottom: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="error-container">
    <i class="bi bi-exclamation-circle error-icon"></i>
    <h1 class="error-title">404 - Page Not Found</h1>
    <p class="error-message">
        The page you are looking for might have been removed, had its name changed, 
        or is temporarily unavailable.
    </p>
    <a href="{{ url_for('admin.show_product') }}" class="btn btn-custom-primary">
        <i class="bi bi-arrow-left"></i> Back to Dashboard
    </a>
</div>
{% endblock %}
