{% extends "base.html" %}

{% block title %}Manage Products{% endblock %}

{% block extra_css %}
<style>
    .search-box {
        max-width: 500px;
        margin: 2rem auto;
    }
    
    .product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-top: 2rem;
    }
    
    .product-card {
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    
    .product-image-container {
        height: 200px;
        overflow: hidden;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        position: relative;
    }
    
    .product-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .no-image-placeholder {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--bg-light);
        color: var(--text-muted);
    }
    
    .product-details {
        padding: 1.5rem;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
    }
    
    .product-actions {
        margin-top: auto;
        display: flex;
        gap: 0.5rem;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem;
    }
    
    .empty-state i {
        font-size: 4rem;
        color: var(--text-muted);
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-box-seam"></i> Manage Products
    </h2>
    <a href="{{ url_for('admin.add_product') }}" class="btn btn-custom-primary">
        <i class="bi bi-plus-lg"></i> Add New Product
    </a>
</div>

<div class="search-box">
    <form class="d-flex" method="GET">
        <input class="form-control me-2" type="search" name="search" 
               placeholder="Search products..." value="{{ request.args.get('search', '') }}">
        <button class="btn btn-custom-primary" type="submit">
            <i class="bi bi-search"></i>
        </button>
    </form>
</div>

{% if products %}
<div class="product-grid">
    {% for product in products %}
    <div class="custom-card product-card hover-scale">
        <div class="product-image-container">
            {% if product.image_path %}
            <img src="/{{ product.image_path }}" class="product-image" alt="{{ product.name }}">
            {% else %}
            <div class="no-image-placeholder">
                <i class="bi bi-image" style="font-size: 3rem;"></i>
            </div>
            {% endif %}
        </div>
        <div class="product-details">
            <h5 class="mb-3">{{ product.name }}</h5>
            <div class="mb-3">
                <p class="mb-2">
                    <i class="bi bi-upc-scan text-primary-custom"></i>
                    <strong>Barcode:</strong> {{ product.barcode }}
                </p>
                <p class="mb-2">
                    <i class="bi bi-tag text-primary-custom"></i>
                    <strong>Price:</strong> ${{ "%.2f"|format(product.price) }}
                </p>
                <p class="mb-0">
                    <i class="bi bi-box text-primary-custom"></i>
                    <strong>Stock:</strong> {{ product.stock }} units
                </p>
            </div>
            <div class="product-actions">
                <a href="{{ url_for('admin.edit_product', product_id=product.id) }}" 
                   class="btn btn-custom-primary flex-grow-1">
                    <i class="bi bi-pencil"></i> Edit
                </a>
                <button type="button" 
                        class="btn btn-danger delete-product"
                        data-product-id="{{ product.id }}"
                        data-product-name="{{ product.name }}">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="empty-state">
    <i class="bi bi-inbox"></i>
    <h3>No Products Found</h3>
    <p class="text-muted">Start by adding some products to your inventory.</p>
    <a href="{{ url_for('admin.add_product') }}" class="btn btn-custom-primary mt-3">
        <i class="bi bi-plus-lg"></i> Add New Product
    </a>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const deleteButtons = document.querySelectorAll('.delete-product');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.dataset.productId;
            const productName = this.dataset.productName;
            
            if (confirm(`Are you sure you want to delete "${productName}"?`)) {
                const deleteUrl = "{{ url_for('admin.delete_product', product_id=0) }}".replace('0', productId);
                window.location.href = deleteUrl;
            }
        });
    });
});
</script>
{% endblock %}
