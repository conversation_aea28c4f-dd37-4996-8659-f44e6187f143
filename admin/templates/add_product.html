{% extends "base.html" %}

{% block title %}Add New Product{% endblock %}

{% block extra_css %}
<style>
    .add-container {
        max-width: 800px;
        margin: 0 auto;
    }
    
    .product-form {
        display: grid;
        gap: 1.5rem;
    }
    
    .preview-image {
        max-width: 200px;
        border-radius: var(--border-radius);
        margin-top: 1rem;
        display: none;
    }
    
    .form-floating {
        position: relative;
    }
    
    .form-floating > .form-control {
        height: calc(3.5rem + 2px);
        padding: 1rem 0.75rem;
    }
    
    .form-floating > label {
        position: absolute;
        top: 0;
        left: 0;
        padding: 1rem 0.75rem;
        pointer-events: none;
        transform-origin: 0 0;
        transition: opacity .1s ease-in-out,transform .1s ease-in-out;
    }
    
    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label {
        opacity: .65;
        transform: scale(.85) translateY(-0.5rem);
    }
</style>
{% endblock %}

{% block content %}
<div class="add-container">
    <div class="custom-card">
        <div class="text-center mb-4">
            <h2><i class="bi bi-plus-circle"></i> Add New Product</h2>
            <p class="text-muted">Enter product details below</p>
        </div>

        <form method="POST" enctype="multipart/form-data" class="product-form needs-validation" novalidate>
            <div class="form-floating">
                <input type="text" class="form-control" id="name" name="name" 
                       placeholder="Product Name" required>
                <label for="name">Product Name</label>
                <div class="invalid-feedback">
                    Please enter a product name
                </div>
            </div>

            <div class="form-floating">
                <input type="text" class="form-control" id="barcode" name="barcode" 
                       placeholder="Barcode" required>
                <label for="barcode">Barcode</label>
                <div class="invalid-feedback">
                    Please enter a barcode
                </div>
            </div>

            <div class="form-floating">
                <input type="number" class="form-control" id="price" name="price" 
                       step="0.01" placeholder="Price" required>
                <label for="price">Price ($)</label>
                <div class="invalid-feedback">
                    Please enter a price
                </div>
            </div>

            <div class="form-floating">
                <input type="number" class="form-control" id="stock" name="stock" 
                       placeholder="Stock" required>
                <label for="stock">Stock Quantity</label>
                <div class="invalid-feedback">
                    Please enter stock quantity
                </div>
            </div>

            <div class="mb-3">
                <label for="image" class="form-label">Product Image</label>
                <input type="file" class="form-control" id="image" name="image" 
                       accept="image/*" onchange="previewImage(this)">
                <img id="preview" class="preview-image">
            </div>

            <div class="d-flex gap-2">
                <a href="{{ url_for('admin.show_product') }}" class="btn btn-secondary flex-grow-1">
                    <i class="bi bi-arrow-left"></i> Back to Products
                </a>
                <button type="submit" class="btn btn-custom-primary flex-grow-1">
                    <i class="bi bi-plus-lg"></i> Add Product
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function previewImage(input) {
    const preview = document.getElementById('preview');
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
        }
        reader.readAsDataURL(input.files[0]);
    }
}

// Form validation
(function() {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })
})()
</script>
{% endblock %}
