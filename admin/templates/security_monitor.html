{% extends "base.html" %}

{% block title %}Security Monitor{% endblock %}

{% block extra_css %}
<style>
    .camera-container {
        background: #000;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
    }
    
    .camera-feed {
        width: 100%;
        height: auto;
        display: block;
    }
    
    .camera-offline {
        background: #2c3e50;
        color: #ecf0f1;
        padding: 100px 20px;
        text-align: center;
        border-radius: 8px;
    }
    
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }
    
    .status-online { background-color: #27ae60; }
    .status-offline { background-color: #e74c3c; }
    .status-warning { background-color: #f39c12; }
    
    .alarm-item {
        border-left: 4px solid #e74c3c;
        background: #fff5f5;
        margin-bottom: 8px;
        padding: 12px;
        border-radius: 4px;
        animation: slideIn 0.3s ease-out;
    }
    
    .alarm-item.info {
        border-left-color: #3498db;
        background: #f0f8ff;
    }
    
    .alarm-item.warning {
        border-left-color: #f39c12;
        background: #fffbf0;
    }
    
    @keyframes slideIn {
        from { opacity: 0; transform: translateX(-20px); }
        to { opacity: 1; transform: translateX(0); }
    }
    
    .product-count {
        background: #3498db;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8em;
        font-weight: bold;
    }
    
    .control-panel {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .data-panel {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0">
            <i class="bi bi-shield-check"></i> Security Monitor
        </h1>
        <div class="d-flex gap-2">
            <span class="badge bg-secondary" id="systemStatus">
                <span class="status-indicator status-offline"></span>
                System Offline
            </span>
            <a href="{{ url_for('admin.show_product') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Products
            </a>
        </div>
    </div>

    <!-- Control Panel -->
    <div class="control-panel">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="mb-3">Camera Control</h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-success" id="startCamera">
                        <i class="bi bi-play-fill"></i> Start Camera
                    </button>
                    <button type="button" class="btn btn-danger" id="stopCamera">
                        <i class="bi bi-stop-fill"></i> Stop Camera
                    </button>
                </div>
            </div>
            <div class="col-md-6">
                <h5 class="mb-3">Add Test Product</h5>
                <div class="input-group">
                    <input type="text" class="form-control" id="testProduct" placeholder="Product name">
                    <button class="btn btn-primary" type="button" id="addTestProduct">
                        <i class="bi bi-plus"></i> Add
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Camera Feed -->
        <div class="col-lg-8">
            <div class="data-panel">
                <h5 class="mb-3">
                    <i class="bi bi-camera-video"></i> Live Camera Feed
                </h5>
                <div class="camera-container">
                    <img id="cameraFeed" class="camera-feed" style="display: none;" 
                         src="{{ url_for('admin.camera_feed') }}" alt="Camera Feed">
                    <div id="cameraOffline" class="camera-offline">
                        <i class="bi bi-camera-video-off" style="font-size: 3rem; margin-bottom: 20px;"></i>
                        <h4>Camera Offline</h4>
                        <p>Click "Start Camera" to begin monitoring</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Panel -->
        <div class="col-lg-4">
            <!-- Detection Data -->
            <div class="data-panel">
                <h5 class="mb-3">
                    <i class="bi bi-eye"></i> Detection Data
                </h5>
                
                <h6 class="text-success">Scanned Products:</h6>
                <div id="scannedProducts" class="mb-3">
                    <p class="text-muted">No products scanned</p>
                </div>
                
                <h6 class="text-info">Camera Detected:</h6>
                <div id="detectedProducts" class="mb-3">
                    <p class="text-muted">No products detected</p>
                </div>
            </div>

            <!-- Alarms Panel -->
            <div class="data-panel">
                <h5 class="mb-3">
                    <i class="bi bi-exclamation-triangle"></i> Security Alarms
                    <span class="badge bg-danger" id="alarmCount">0</span>
                </h5>
                <div id="alarmsList" style="max-height: 300px; overflow-y: auto;">
                    <p class="text-muted">No alarms</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let isMonitoring = false;
let dataUpdateInterval;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    checkCameraStatus();
    
    // Event listeners
    document.getElementById('startCamera').addEventListener('click', startCamera);
    document.getElementById('stopCamera').addEventListener('click', stopCamera);
    document.getElementById('addTestProduct').addEventListener('click', addTestProduct);
    
    // Enter key for test product
    document.getElementById('testProduct').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            addTestProduct();
        }
    });
});

function checkCameraStatus() {
    fetch('/admin/api/camera/status')
        .then(response => response.json())
        .then(data => {
            updateSystemStatus(data);
            if (data.is_running) {
                startDataUpdates();
                showCameraFeed();
            } else {
                stopDataUpdates();
                hideCameraFeed();
            }
        })
        .catch(error => {
            console.error('Error checking camera status:', error);
            updateSystemStatus({is_running: false, has_camera: false, model_loaded: false});
        });
}

function updateSystemStatus(status) {
    const statusElement = document.getElementById('systemStatus');
    const indicator = statusElement.querySelector('.status-indicator');
    
    if (status.is_running) {
        statusElement.innerHTML = '<span class="status-indicator status-online"></span>System Online';
        statusElement.className = 'badge bg-success';
    } else if (status.has_camera && status.model_loaded) {
        statusElement.innerHTML = '<span class="status-indicator status-warning"></span>System Ready';
        statusElement.className = 'badge bg-warning';
    } else {
        statusElement.innerHTML = '<span class="status-indicator status-offline"></span>System Offline';
        statusElement.className = 'badge bg-secondary';
    }
}

function startCamera() {
    fetch('/admin/camera/start')
        .then(response => {
            if (response.ok) {
                setTimeout(() => {
                    checkCameraStatus();
                }, 2000);
            }
        })
        .catch(error => console.error('Error starting camera:', error));
}

function stopCamera() {
    fetch('/admin/camera/stop')
        .then(response => {
            if (response.ok) {
                setTimeout(() => {
                    checkCameraStatus();
                }, 1000);
            }
        })
        .catch(error => console.error('Error stopping camera:', error));
}

function showCameraFeed() {
    document.getElementById('cameraFeed').style.display = 'block';
    document.getElementById('cameraOffline').style.display = 'none';
}

function hideCameraFeed() {
    document.getElementById('cameraFeed').style.display = 'none';
    document.getElementById('cameraOffline').style.display = 'block';
}

function startDataUpdates() {
    if (dataUpdateInterval) return;
    
    dataUpdateInterval = setInterval(updateData, 1000); // Update every second
    updateData(); // Initial update
}

function stopDataUpdates() {
    if (dataUpdateInterval) {
        clearInterval(dataUpdateInterval);
        dataUpdateInterval = null;
    }
}

function updateData() {
    fetch('/admin/api/camera/data')
        .then(response => response.json())
        .then(data => {
            updateProductLists(data);
            updateAlarms(data.alarms);
        })
        .catch(error => console.error('Error updating data:', error));
}

function updateProductLists(data) {
    // Update scanned products
    const scannedDiv = document.getElementById('scannedProducts');
    if (Object.keys(data.scanned_products).length === 0) {
        scannedDiv.innerHTML = '<p class="text-muted">No products scanned</p>';
    } else {
        let html = '';
        for (const [product, count] of Object.entries(data.scanned_products)) {
            html += `<div class="d-flex justify-content-between align-items-center mb-1">
                        <span>${product}</span>
                        <span class="product-count">${count}</span>
                     </div>`;
        }
        scannedDiv.innerHTML = html;
    }
    
    // Update detected products
    const detectedDiv = document.getElementById('detectedProducts');
    if (Object.keys(data.detected_products).length === 0) {
        detectedDiv.innerHTML = '<p class="text-muted">No products detected</p>';
    } else {
        let html = '';
        for (const [product, count] of Object.entries(data.detected_products)) {
            html += `<div class="d-flex justify-content-between align-items-center mb-1">
                        <span>${product}</span>
                        <span class="product-count">${count}</span>
                     </div>`;
        }
        detectedDiv.innerHTML = html;
    }
}

function updateAlarms(alarms) {
    const alarmsList = document.getElementById('alarmsList');
    const alarmCount = document.getElementById('alarmCount');
    
    alarmCount.textContent = alarms.length;
    
    if (alarms.length === 0) {
        alarmsList.innerHTML = '<p class="text-muted">No alarms</p>';
    } else {
        let html = '';
        // Show most recent alarms first
        alarms.reverse().forEach(alarm => {
            html += `<div class="alarm-item ${alarm.type}">
                        <div class="d-flex justify-content-between">
                            <strong>${alarm.message}</strong>
                            <small>${alarm.timestamp}</small>
                        </div>
                     </div>`;
        });
        alarmsList.innerHTML = html;
    }
}

function addTestProduct() {
    const productName = document.getElementById('testProduct').value.trim();
    if (!productName) return;
    
    fetch('/admin/api/camera/add-product', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_name: productName,
            quantity: 1
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('testProduct').value = '';
            // Data will be updated automatically by the interval
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => console.error('Error adding test product:', error));
}
</script>
{% endblock %}
