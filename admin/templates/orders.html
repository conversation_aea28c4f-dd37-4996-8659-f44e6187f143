{% extends "base.html" %}

{% block title %}Orders{% endblock %}

{% block extra_css %}
<style>
    .orders-container {
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .order-card {
        margin-bottom: 1.5rem;
    }
    
    .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid var(--border-color);
    }
    
    .order-items {
        margin-bottom: 1rem;
    }
    
    .order-item {
        display: flex;
        align-items: center;
        padding: 0.5rem;
        border-radius: var(--border-radius);
        background-color: var(--bg-light);
        margin-bottom: 0.5rem;
    }
    
    .order-item:last-child {
        margin-bottom: 0;
    }
    
    .item-image {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: var(--border-radius);
        margin-right: 1rem;
    }
    
    .item-details {
        flex-grow: 1;
    }
    
    .item-quantity {
        margin-left: 1rem;
        font-weight: bold;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem;
    }
    
    .empty-state i {
        font-size: 4rem;
        color: var(--text-muted);
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="orders-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
            <i class="bi bi-cart-check"></i> Orders
        </h2>
    </div>

    {% if orders %}
        {% for order in orders %}
        <div class="custom-card order-card">
            <div class="order-header">
                <div>
                    <h5 class="mb-1">Order #{{ loop.index }}</h5>
                    <p class="text-muted mb-0">
                        <i class="bi bi-clock"></i> {{ order.order_date }}
                    </p>
                </div>
            </div>

            <div class="order-items">
                {% for item in order['items'] %}
                <div class="order-item">
                    {% if item.image_path %}
                    <img src="/{{ item.image_path }}" class="item-image" alt="{{ item.product_name }}">
                    {% else %}
                    <div class="item-image bg-light d-flex align-items-center justify-content-center">
                        <i class="bi bi-image text-muted"></i>
                    </div>
                    {% endif %}
                    <div class="item-details">
                        <h6 class="mb-1">{{ item.product_name }}</h6>
                        <p class="text-muted mb-0">${{ "%.2f"|format(item.product_price) }}</p>
                    </div>
                    <div class="item-quantity">
                        x{{ item.quantity }}
                    </div>
                </div>
                {% endfor %}
            </div>

            <div class="d-flex justify-content-between align-items-center">
                <p class="mb-0">
                    <strong>Total Items:</strong> {{ order.total_items }}
                </p>
                <p class="mb-0">
                    <strong>Total Amount:</strong> ${{ "%.2f"|format(order.total_amount) }}
                </p>
                <button type="button" class="btn btn-info btn-sm view-details" data-order-id="{{ order.id }}">
                    <i class="bi bi-eye"></i> View Details
                </button>
                <button type="button" class="btn btn-danger btn-sm delete-order" data-order-id="{{ order.id }}">
                    <i class="bi bi-trash"></i> Delete Order
                </button>
            </div>
        </div>
        {% endfor %}
    {% else %}
    <div class="empty-state">
        <i class="bi bi-inbox"></i>
        <h3>No Orders Found</h3>
        <p class="text-muted">There are no orders in the system yet.</p>
    </div>
    {% endif %}

    <!-- Pagination Controls -->
    <nav aria-label="Order pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if page > 1 %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page - 1 }}">Previous</a>
            </li>
            {% endif %}
            {% for p in range(1, total_pages + 1) %}
            <li class="page-item {% if p == page %}active{% endif %}">
                <a class="page-link" href="?page={{ p }}">{{ p }}</a>
            </li>
            {% endfor %}
            {% if page < total_pages %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page + 1 }}">Next</a>
            </li>
            {% endif %}
        </ul>
    </nav>

    <!-- Order Details Modal -->
    <div class="modal fade" id="orderDetailsModal" tabindex="-1" aria-labelledby="orderDetailsModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="orderDetailsModalLabel">Order Details</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div id="order-details-content">
              <!-- Order details will be loaded here by JS -->
            </div>
          </div>
        </div>
      </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // AJAX Delete
    const deleteButtons = document.querySelectorAll('.delete-order');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const orderId = this.dataset.orderId;
            if (confirm('Are you sure you want to delete this order?')) {
                fetch(`/admin/api/delete_order/${orderId}`, {
                    method: 'POST',
                    headers: { 'X-Requested-With': 'XMLHttpRequest' }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove the order card from the DOM
                        button.closest('.order-card').remove();
                    } else {
                        alert('Error deleting order: ' + (data.error || 'Unknown error'));
                    }
                });
            }
        });
    });
    // View Details Modal
    const detailsButtons = document.querySelectorAll('.view-details');
    detailsButtons.forEach(button => {
        button.addEventListener('click', function() {
            const orderId = this.dataset.orderId;
            fetch(`/admin/api/order_details/${orderId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const order = data.order;
                        let html = `<p><strong>Order ID:</strong> ${order.id}</p>`;
                        html += `<p><strong>Date:</strong> ${order.order_date}</p>`;
                        html += `<p><strong>Total Amount:</strong> $${order.total_amount.toFixed(2)}</p>`;
                        html += `<p><strong>Total Items:</strong> ${order.total_items}</p>`;
                        html += `<h5>Items:</h5>`;
                        html += `<ul class='list-group mb-3'>`;
                        order.items.forEach(item => {
                            html += `<li class='list-group-item d-flex align-items-center'>`;
                            if (item.image_path) {
                                html += `<img src='/${item.image_path}' alt='${item.product_name}' style='width:40px;height:40px;object-fit:cover;margin-right:10px;'>`;
                            }
                            html += `<span><strong>${item.product_name}</strong> (x${item.quantity}) - $${item.product_price.toFixed(2)}</span>`;
                            html += `</li>`;
                        });
                        html += `</ul>`;
                        document.getElementById('order-details-content').innerHTML = html;
                        var modal = new bootstrap.Modal(document.getElementById('orderDetailsModal'));
                        modal.show();
                    } else {
                        document.getElementById('order-details-content').innerHTML = `<div class='alert alert-danger'>${data.error}</div>`;
                        var modal = new bootstrap.Modal(document.getElementById('orderDetailsModal'));
                        modal.show();
                    }
                });
        });
    });
});
</script>
{% endblock %} 