# نظام عربة التسوق الذكية المتكامل

نظام متكامل للتسوق الذكي يجمع بين إدارة المتجر ونظام مراقبة أمني. يوفر واجهة سطح مكتب سهلة الاستخدام تمكنك من إدارة التطبيق والوصول إلى جميع الوظائف من مكان واحد.

## المميزات الرئيسية

1. **تطبيق ويب مبني على Flask**: 
   - إدارة المنتجات والمخزون
   - سلة تسوق ذكية للعملاء
   - نظام تسجيل الدخول للمدير والعملاء
   - واجهة متجاوبة وسهلة الاستخدام
   
2. **واجهة سطح مكتب متكاملة**:
   - تشغيل التطبيق بسهولة بنقرة واحدة
   - التحكم الكامل في جميع مكونات النظام
   - عرض حالة النظام ومراقبته

3. **نظام مراقبة أمني**:
   - استخدام YOLOv8 للكشف عن الأشخاص والأشياء
   - مراقبة المتجر في الوقت الفعلي
   - تنبيهات وإشعارات للأحداث المهمة

## متطلبات النظام

- نظام التشغيل: Windows 10 أو أحدث
- الذاكرة: 4 جيجابايت RAM (على الأقل)
- المعالج: Intel i3 أو ما يعادله (أو أفضل)
- كاميرا ويب متصلة (لوظائف المراقبة)

## طريقة التثبيت

### طريقة 1: تثبيت التطبيق المُجمّع

1. قم بتنزيل أحدث إصدار من صفحة الإصدارات.
2. قم باستخراج الملفات.
3. قم بتشغيل ملف `SmartCartSystem.exe`.

### طريقة 2: تثبيت من المصدر

1. قم بتثبيت Python 3.9+ على جهازك.
2. استنسخ المستودع: `git clone https://github.com/yourusername/smart-cart-system.git`
3. انتقل إلى مجلد المشروع: `cd smart-cart-system`
4. قم بتثبيت المتطلبات: `pip install -r requirements.txt`
5. قم بتشغيل التطبيق: `python enhanced_desktop.py`

### بناء التطبيق من المصدر

لإنشاء نسخة قابلة للتوزيع:

1. قم بتثبيت المتطلبات: `pip install -r requirements.txt`
2. قم بتشغيل سكربت البناء: `build_enhanced_app.bat`
3. سيتم إنشاء التطبيق في مجلد `dist/SmartCartSystem`.

## طريقة الاستخدام

1. **بدء تشغيل التطبيق**:
   - قم بتشغيل `SmartCartSystem.exe` أو `python enhanced_desktop.py`
   - ستظهر لك واجهة النظام الرئيسية

2. **إدارة المتجر**:
   - اضغط على زر "تشغيل نظام المتجر" لفتح تطبيق إدارة المتجر في المتصفح
   - قم بتسجيل الدخول كمدير باستخدام:
     - اسم المستخدم: admin
     - كلمة المرور: 1234

3. **نظام المراقبة**:
   - اضغط على زر "تشغيل نظام المراقبة" لفتح نظام المراقبة الأمني
   - تأكد من توصيل كاميرا ويب بجهازك
   - يمكنك بدء المراقبة ومشاهدة التحليل في الوقت الفعلي

## استخدام نموذج YOLO مخصص

لاستخدام نموذج YOLO مُدرب خصيصًا:

1. ضع ملف النموذج (بامتداد .pt) في مجلد المشروع.
2. تأكد من تحرير الملف `enhanced_desktop.py` وتغيير قيمة `model_path` في طريقة `start_security_monitor`.

## استخدام التطبيق على Raspberry Pi

لتشغيل التطبيق على Raspberry Pi، اتبع هذه الخطوات:

1. قم بتثبيت Raspberry Pi OS (المنسق الكامل مع سطح المكتب).
2. قم بتثبيت المتطلبات:
   ```
   sudo apt update
   sudo apt install python3-pip python3-opencv python3-tk libopencv-dev
   pip3 install -r requirements.txt
   ```
3. قم بتشغيل التطبيق:
   ```
   python3 enhanced_desktop.py
   ```

## استكشاف الأخطاء وإصلاحها

- **مشكلة في تشغيل خادم Flask**: تأكد من أن المنفذ 5000 غير مستخدم من قبل تطبيق آخر.
- **مشكلة في نظام المراقبة**: تأكد من توصيل كاميرا ويب وتثبيت حزم OpenCV بشكل صحيح.
- **أخطاء في تحميل نموذج YOLO**: تأكد من وجود ملف النموذج في المكان الصحيح وأن الإنترنت متاح لتنزيل النموذج الافتراضي عند الحاجة.

## المساهمة في المشروع

نرحب بمساهماتكم لتحسين النظام. يرجى اتباع الخطوات التالية:

1. قم بعمل Fork للمشروع
2. قم بإنشاء فرع لميزتك: `git checkout -b feature/amazing-feature`
3. قم بالالتزام بالتغييرات: `git commit -m 'إضافة ميزة رائعة'`
4. قم بدفع التغييرات إلى الفرع: `git push origin feature/amazing-feature`
5. قم بفتح Pull Request

## الترخيص

هذا المشروع مرخص بموجب [ترخيص MIT](LICENSE). 