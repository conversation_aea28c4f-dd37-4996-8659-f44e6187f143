[Unit]
Description=Smart Shopping Cart System
After=network.target
Wants=network.target

[Service]
Type=simple
User=pi
Group=pi
WorkingDirectory=/home/<USER>/Downloads/ui+admin
Environment=PATH=/usr/bin:/usr/local/bin:/home/<USER>/.local/bin
Environment=PYTHONPATH=/home/<USER>/Downloads/ui+admin
ExecStart=/usr/bin/python3 /home/<USER>/Downloads/ui+admin/app.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
