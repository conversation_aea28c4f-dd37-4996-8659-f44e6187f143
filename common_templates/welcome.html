<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Smart Shopping Cart</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <style>
        .welcome-hero {
            background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url("{{ url_for('static', filename='images/shopping-cart-bg.jpg') }}");
            background-size: cover;
            background-position: center;
            min-height: 80vh;
            display: flex;
            align-items: center;
            color: white;
        }

        .feature-card {
            text-align: center;
            padding: 2rem;
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-10px);
        }

        .feature-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .cta-section {
            background-color: var(--primary-color);
            color: white;
            padding: 4rem 0;
        }

        /* Custom Button Styles */
        .btn-shopping {
            padding: 20px 40px;
            font-size: 1.2rem;
            border-radius: 15px;
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 15px;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            min-width: 280px;
        }

        .btn-shopping:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4);
            color: white;
            text-decoration: none;
        }

        .btn-shopping i {
            font-size: 2.5rem;
        }

        .btn-content {
            text-align: left;
        }

        .btn-title {
            font-size: 1.4rem;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .btn-subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .btn-admin {
            padding: 12px 25px;
            font-size: 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-admin:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="welcome-hero">
        <div class="container text-center">
            <h1 class="display-4 mb-4">Welcome to Smart Shopping Cart</h1>
            <p class="lead mb-4">A unique shopping experience combining convenience and technology</p>



            <div class="d-flex justify-content-center align-items-center gap-4 flex-wrap">
                <!-- Main Start Shopping Button (Large) -->
                <a href="{{ url_for('customer.auto_start_shopping') }}" class="btn btn-success btn-shopping">
                    <i class="bi bi-cart-plus"></i>
                    <div class="btn-content">
                        <div class="btn-title">Start Shopping</div>
                        <div class="btn-subtitle">AI-Powered Smart Cart</div>
                    </div>
                </a>

                <!-- Admin Login Button (Smaller) -->
                <a href="{{ url_for('admin.login') }}" class="btn btn-outline-light btn-admin">
                    <i class="bi bi-shield-lock"></i> Admin Login
                </a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">Smart Shopping Cart Features</h2>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="feature-card">
                        <i class="bi bi-camera-video feature-icon"></i>
                        <h3>Smart Detection</h3>
                        <p>AI-powered camera automatically detects products in your cart</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card">
                        <i class="bi bi-shield-check feature-icon"></i>
                        <h3>Security Monitoring</h3>
                        <p>Real-time theft prevention with intelligent alarm system</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card">
                        <i class="bi bi-lightning-charge feature-icon"></i>
                        <h3>Instant Checkout</h3>
                        <p>No more scanning - products are added automatically as you shop</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container text-center">
            <h2 class="mb-4">Ready for Smart Shopping?</h2>
            <p class="lead mb-4">Join us today and enjoy a unique shopping experience</p>
            <a href="{{ url_for('customer.auto_start_shopping') }}" class="btn btn-light btn-lg">
                <i class="bi bi-cart"></i> Start Shopping Now
            </a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-0">© 2024 Smart Shopping Cart. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <p class="mb-0">Developed by Smart Shopping Cart Team</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>


</body>
</html>