/* Common Styles */
:root {
    --primary-color: #6c5ce7;
    --primary-dark: #5849c2;
    --secondary-color: #a363d9;
    --text-color: #2d3436;
    --text-muted: #636e72;
    --bg-light: #f8f9fa;
    --white: #ffffff;
    --shadow: 0 4px 6px rgba(0,0,0,0.1);
    --border-radius: 15px;
    --transition: all 0.3s ease;
}

body {
    font-family: 'N<PERSON><PERSON>', 'Roboto', 'Cairo', 'Segoe UI', 'Arial', sans-serif;
    background: linear-gradient(120deg, #f8f9fa 0%, #e0e7ff 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    font-size: 1.15rem;
    letter-spacing: 0.01em;
    position: relative;
    line-height: 1.7;
    transition: background 1s ease;
    animation: gradientBG 12s ease-in-out infinite alternate;
}

@keyframes gradientBG {
    0% { background: linear-gradient(120deg, #f8f9fa 0%, #e0e7ff 100%); }
    50% { background: linear-gradient(120deg, #e0e7ff 0%, #f8f9fa 100%); }
    100% { background: linear-gradient(120deg, #f8f9fa 0%, #e0e7ff 100%); }
}

body::before {
    content: '';
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    background-image: url('https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/icons/cart.svg');
    background-repeat: repeat;
    background-size: 120px 120px;
    opacity: 0.025;
    z-index: 0;
    pointer-events: none;
}

/* Header Banner */
.header-banner {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 0.75rem 0;
    text-align: center;
    font-weight: 500;
    font-size: 1.1rem;
}

.header-banner i {
    margin-right: 0.5rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Navbar Styles */
.navbar {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    padding: 1rem 0;
    box-shadow: var(--shadow);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 600;
}

/* Card Styles */
.custom-card {
    background: rgba(255,255,255,0.85);
    border-radius: 30px;
    box-shadow: 0 8px 32px rgba(108,92,231,0.13);
    padding: 2.2rem;
    transition: var(--transition), box-shadow 0.3s;
    backdrop-filter: blur(6px) saturate(120%);
    border: 1px solid rgba(108,92,231,0.08);
}

.custom-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 16px 48px rgba(108,92,231,0.18);
}

/* Button Styles */
.btn,
.btn-primary,
.btn-danger,
.btn-outline-secondary,
.btn-custom-primary {
    font-size: 1.18rem !important;
    padding: 1.05rem 2.3rem !important;
    border-radius: 24px !important;
    font-weight: 800 !important;
    min-width: 56px;
    min-height: 56px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-sm {
    font-size: 1.05rem !important;
    padding: 0.7rem 1.5rem !important;
    border-radius: 18px !important;
    min-width: 40px;
    min-height: 40px;
}

.btn-custom-primary i {
    font-size: 1.2em;
}

.btn-custom-primary:hover {
    background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
    color: var(--white);
    box-shadow: 0 6px 24px rgba(108,92,231,0.18);
    transform: scale(1.04);
}

/* Form Styles */
.form-control {
    border-radius: 10px;
    padding: 0.8rem;
    border: 2px solid #e2e8f0;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: none;
}

/* Table Styles */
.custom-table {
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.custom-table th {
    background: var(--primary-color);
    color: var(--white);
    font-weight: 600;
    padding: 1rem;
}

.custom-table td {
    padding: 1rem;
    vertical-align: middle;
}

/* Badge Styles */
.badge-custom {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 500;
}

/* Image Styles */
.img-custom {
    border-radius: var(--border-radius);
    object-fit: cover;
}

/* Animation Classes */
.hover-scale {
    transition: var(--transition);
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* Utility Classes */
.text-primary-custom {
    color: var(--primary-color);
}

.bg-primary-custom {
    background-color: var(--primary-color);
}

.shadow-custom {
    box-shadow: var(--shadow);
}

/* RTL Specific Styles */
[dir="rtl"] .ms-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

[dir="rtl"] .me-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .custom-card {
        margin-bottom: 1rem;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
    .btn,
    .btn-primary,
    .btn-danger,
    .btn-outline-secondary,
    .btn-custom-primary {
        font-size: 1.05rem !important;
        padding: 0.8rem 1.2rem !important;
        border-radius: 18px !important;
    }
    .btn-sm {
        font-size: 0.98rem !important;
        padding: 0.5rem 1rem !important;
        border-radius: 14px !important;
    }
}

.smart-banner {
    background: rgba(108,92,231,0.85);
    color: var(--white);
    padding: 1.5rem 0.5rem;
    text-align: center;
    font-size: 1.5rem;
    font-weight: 800;
    border-radius: 0 0 30px 30px;
    margin-bottom: 2.5rem;
    box-shadow: 0 8px 32px rgba(108,92,231,0.13);
    position: relative;
    z-index: 1;
    letter-spacing: 0.03em;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.7rem;
}

.smart-banner i, .smart-banner .emoji {
    margin-right: 0.7rem;
    font-size: 2em;
    vertical-align: middle;
    animation: bounce 2.5s infinite;
}

.smart-banner .emoji {
    margin-right: 0.5rem;
    font-size: 2.2em;
    animation: none;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-8px); }
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Nunito', 'Roboto', 'Cairo', 'Segoe UI', 'Arial', sans-serif;
    font-weight: 900;
    letter-spacing: 0.02em;
}

section, .custom-card, .smart-banner {
    margin-bottom: 2rem;
} 