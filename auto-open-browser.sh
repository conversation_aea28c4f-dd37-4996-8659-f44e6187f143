#!/bin/bash

# Auto-open browser script for Smart Shopping Cart
echo "🚀 Starting Smart Shopping Cart browser..."

# Wait for system to fully boot and service to start
sleep 30

# Wait for the Flask app to be ready
echo "⏳ Waiting for Smart Shopping Cart to be ready..."
while ! curl -s http://localhost:5000 > /dev/null; do
    echo "⏳ Waiting for service..."
    sleep 5
done

echo "✅ Smart Shopping Cart is ready!"

# Open browser in fullscreen/kiosk mode
if command -v chromium-browser &> /dev/null; then
    echo "🌐 Opening Chromium browser..."
    chromium-browser --start-fullscreen --kiosk http://localhost:5000 &
elif command -v firefox &> /dev/null; then
    echo "🌐 Opening Firefox browser..."
    firefox --kiosk http://localhost:5000 &
else
    echo "🌐 Opening default browser..."
    xdg-open http://localhost:5000 &
fi

echo "🎉 Smart Shopping Cart opened successfully!"
