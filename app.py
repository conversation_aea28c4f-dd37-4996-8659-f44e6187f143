from flask import Flask, redirect, url_for, session, render_template, request
from admin.routes import admin_bp
from customer.routes import customer_bp
import os

# Ensure correct database path
current_dir = os.path.dirname(os.path.abspath(__file__))
os.environ['DATABASE_PATH'] = os.path.join(current_dir, 'products.db')

# Initialize Flask app with common templates
app = Flask(__name__, template_folder='common_templates')
app.secret_key = b'_5#y2L"F4Q8z\n\xec]/'  # Required for session management

# Register Blueprints for different sections
app.register_blueprint(admin_bp)
app.register_blueprint(customer_bp)

# Welcome page route
@app.route('/')
def home():
    # Check if user was redirected from another page
    redirect_from = request.args.get('from')
    
    # Pass any redirect information to the template
    return render_template('welcome.html', redirect_from=redirect_from)

# Redirect to customer shopping interface
@app.route('/shop')
def customer_home():
    return redirect(url_for('customer.index'))

# Redirect to admin interface
@app.route('/admin')
def admin_home():
    # If admin is already logged in, redirect to products page
    if session.get('admin_logged_in'):
        return redirect(url_for('admin.show_product'))
    return redirect(url_for('admin.login'))

# Error handlers
@app.errorhandler(404)
def page_not_found(e):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_server_error(e):
    return render_template('500.html'), 500

if __name__ == '__main__':
    print(f"✅ Database path: {os.environ['DATABASE_PATH']}")
    print("✅ Flask app running on http://127.0.0.1:5000")
    app.run(debug=True)
