<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Smart Shopping Cart{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        .top-banner {
            background: linear-gradient(135deg, #6c5ce7, #a363d9);
            color: white;
            padding: 12px 0;
            text-align: center;
            font-size: 1.1rem;
            font-weight: 500;
        }
        .top-banner i {
            margin-right: 8px;
            animation: sparkle 2s infinite;
        }
        @keyframes sparkle {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.8; }
            100% { transform: scale(1); opacity: 1; }
        }
        .navbar {
            background: linear-gradient(135deg, #6c5ce7, #a363d9);
            padding: 1rem 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Top Banner -->
    <div class="top-banner">
        <i class="bi bi-stars"></i>Enjoy in our store!
    </div>

    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-cart4"></i> Smart Shopping Cart
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'customer.index' %}active{% endif %}"
                           href="{{ url_for('customer.index') }}">
                            <i class="bi bi-upc-scan"></i> Manual Scan
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'customer.smart_shopping' %}active{% endif %}"
                           href="{{ url_for('customer.smart_shopping') }}">
                            <i class="bi bi-camera-video"></i> Smart Shopping
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'customer.cart' %}active{% endif %}" 
                           href="{{ url_for('customer.cart') }}">
                            <i class="bi bi-cart"></i> Shopping Cart
                            <span class="badge bg-light text-dark" id="cartCount">0</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <main class="flex-grow-1 py-4">
        <div class="container">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            {% block content %}{% endblock %}
        </div>
    </main>

    <footer class="bg-dark text-light py-4 mt-auto">
        <div class="container">
            <div class="row">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-0">© 2024 Smart Shopping Cart. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <p class="mb-0">Shop Smart, Shop Easy</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Update cart count on page load
        fetch('/customer/get-cart-count')
            .then(response => response.json())
            .then(data => {
                document.getElementById('cartCount').textContent = data.count;
            });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html> 