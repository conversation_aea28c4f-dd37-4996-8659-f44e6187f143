{% extends "base.html" %}

{% block title %}Smart Shopping Experience{% endblock %}

{% block extra_css %}
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<style>
    body {
        font-family: 'Poppins', sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .shopping-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        margin: 20px auto;
        max-width: 1200px;
    }

    .welcome-header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        padding: 30px;
        border-radius: 20px 20px 0 0;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .welcome-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
        animation: float 20s infinite linear;
    }

    @keyframes float {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    .welcome-content {
        position: relative;
        z-index: 2;
    }

    .shopping-mascot {
        font-size: 4rem;
        animation: bounce 2s infinite;
        margin-bottom: 15px;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }

    .status-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 2px solid transparent;
        transition: all 0.3s ease;
    }

    .status-card.active {
        border-color: #28a745;
        box-shadow: 0 10px 30px rgba(40,167,69,0.2);
    }

    .detection-panel {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin: 10px;
    }

    .cart-panel {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin: 10px;
    }

    .product-card {
        background: rgba(255,255,255,0.95);
        border-radius: 12px;
        padding: 15px;
        margin: 10px 0;
        border-left: 4px solid #4facfe;
        transition: all 0.3s ease;
        animation: slideInRight 0.5s ease-out;
    }

    .product-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    @keyframes slideInRight {
        from { opacity: 0; transform: translateX(30px); }
        to { opacity: 1; transform: translateX(0); }
    }

    .btn-friendly {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102,126,234,0.3);
    }

    .btn-friendly:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102,126,234,0.4);
        color: white;
    }

    .btn-cart {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(240,147,251,0.3);
    }

    .btn-cart:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(240,147,251,0.4);
        color: white;
    }

    .progress-steps {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 20px 0;
        flex-wrap: wrap;
    }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0 15px;
        opacity: 0.5;
        transition: all 0.3s ease;
    }

    .step.active {
        opacity: 1;
        transform: scale(1.1);
    }

    .step-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        margin-bottom: 8px;
        box-shadow: 0 5px 15px rgba(79,172,254,0.3);
    }

    .step-text {
        font-size: 0.9rem;
        font-weight: 500;
        text-align: center;
        color: #666;
    }

    .detection-counter {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border-radius: 50%;
        width: 80px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        font-weight: bold;
        margin: 0 auto 15px;
        box-shadow: 0 10px 30px rgba(79,172,254,0.3);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .alert-friendly {
        border-radius: 12px;
        border: none;
        padding: 15px 20px;
        margin: 10px 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 5px 15px rgba(102,126,234,0.2);
    }

    .security-alert {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
        border-radius: 12px;
        padding: 15px;
        margin: 8px 0;
        animation: alertPulse 0.5s ease-in-out 3;
        box-shadow: 0 5px 15px rgba(255,107,107,0.3);
    }

    @keyframes alertPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.02); }
        100% { transform: scale(1); }
    }

    .loading-spinner {
        display: inline-block;
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        border-top-color: white;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    .celebration {
        animation: celebrate 0.6s ease-out;
    }

    @keyframes celebrate {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    /* Navigation Barcode Input Styling */
    .nav-barcode-section {
        flex: 1;
        max-width: 400px;
        margin: 0 20px;
    }

    .nav-barcode-input {
        border: 2px solid #e0e0e0;
        border-radius: 25px 0 0 25px;
        padding: 12px 20px;
        font-size: 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
        background: white;
    }

    .nav-barcode-input:focus {
        border-color: #4facfe;
        box-shadow: 0 0 0 0.2rem rgba(79,172,254,0.25);
        outline: none;
    }

    .nav-barcode-input::placeholder {
        color: #999;
        font-style: italic;
    }

    .btn-nav-scan {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border: none;
        color: white;
        padding: 12px 20px;
        border-radius: 0 25px 25px 0;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 3px 10px rgba(79,172,254,0.3);
    }

    .btn-nav-scan:hover {
        transform: translateY(-1px);
        box-shadow: 0 5px 15px rgba(79,172,254,0.4);
        color: white;
    }

    .btn-nav-scan:disabled {
        opacity: 0.7;
        transform: none;
    }

    /* Cart Summary Section */
    .cart-summary-section {
        background: rgba(255,255,255,0.9);
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 2px solid transparent;
        transition: all 0.3s ease;
    }

    .cart-summary-section:hover {
        border-color: #4facfe;
        box-shadow: 0 8px 25px rgba(79,172,254,0.2);
    }

    .cart-info {
        margin-bottom: 15px;
    }

    .progress-steps {
        justify-content: flex-start;
        flex-wrap: wrap;
    }

    /* How It Works Section */
    .how-it-works-section {
        background: rgba(255,255,255,0.9);
        border-radius: 15px;
        padding: 30px;
        margin: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .step-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        height: 100%;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .step-card:hover {
        transform: translateY(-5px);
        border-color: #4facfe;
        box-shadow: 0 10px 25px rgba(79,172,254,0.2);
    }

    .step-icon-large {
        font-size: 3rem;
        margin-bottom: 15px;
        color: #4facfe;
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: bounce 2s infinite;
        display: block;
    }

    .step-icon-large i {
        font-size: 3rem;
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        display: inline-block;
    }

    .step-title {
        color: #667eea;
        font-weight: 600;
        margin: 0;
        font-size: 1rem;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-8px); }
        60% { transform: translateY(-4px); }
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .nav-barcode-section {
            max-width: 250px;
            margin: 0 10px;
        }

        .nav-barcode-input {
            font-size: 0.9rem;
            padding: 10px 15px;
        }

        .btn-nav-scan {
            padding: 10px 15px;
            font-size: 0.9rem;
        }

        .progress-steps {
            gap: 10px;
            justify-content: center;
        }

        .step {
            margin: 0 5px;
        }

        .cart-summary-section {
            margin-top: 20px;
            padding: 15px;
        }

        .cart-info {
            margin-bottom: 10px;
        }
    }


</style>
{% endblock %}

{% block content %}
<div class="shopping-container">
    <!-- How Smart Shopping Works Guide -->
    <div class="how-it-works-section">
        <h2 class="text-center mb-4" style="color: #667eea; font-weight: 600;">
            <i class="bi bi-lightbulb"></i> How Smart Shopping Works
        </h2>
        <div class="row text-center">
            <div class="col-md-3 mb-3">
                <div class="step-card">
                    <div class="step-icon-large">
                        <i class="bi bi-cart-plus"></i>
                    </div>
                    <h6 class="step-title">Place items in your cart</h6>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="step-card">
                    <div class="step-icon-large">
                        <i class="bi bi-camera-video"></i>
                    </div>
                    <h6 class="step-title">AI camera detects them</h6>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="step-card">
                    <div class="step-icon-large">
                        <i class="bi bi-lightning-charge"></i>
                    </div>
                    <h6 class="step-title">Get instant alerts</h6>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="step-card">
                    <div class="step-icon-large">
                        <i class="bi bi-credit-card"></i>
                    </div>
                    <h6 class="step-title">Checkout when ready</h6>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Bar with Barcode Input -->
    <div class="d-flex justify-content-between align-items-center p-3" style="background: rgba(255,255,255,0.9);">
        <button class="btn btn-friendly" id="backToHomeBtn">
            <i class="bi bi-arrow-left"></i> Back to Home
        </button>

        <!-- Barcode Input in Navigation -->
        <div class="nav-barcode-section">
            <div class="input-group">
                <input type="text" class="form-control nav-barcode-input" id="barcodeInput"
                       placeholder="Enter or scan barcode..."
                       autocomplete="off" autocapitalize="off">
                <button class="btn btn-nav-scan" type="button" id="addBarcodeBtn">
                    <i class="bi bi-plus-circle"></i> Add
                </button>
            </div>
        </div>


    </div>

    <!-- Progress Steps & Cart Summary Row -->
    <div class="row align-items-center" style="background: rgba(255,255,255,0.6); padding: 20px; margin: 0;">
        <!-- Left: Progress Steps -->
        <div class="col-md-8">
            <div class="progress-steps">
                <div class="step active" id="step1">
                    <div class="step-icon"><i class="bi bi-camera-video"></i></div>
                    <div class="step-text">Camera Active</div>
                </div>
                <div class="step" id="step2">
                    <div class="step-icon"><i class="bi bi-eye"></i></div>
                    <div class="step-text">Detecting Products</div>
                </div>
                <div class="step" id="step3">
                    <div class="step-icon"><i class="bi bi-cart-check"></i></div>
                    <div class="step-text">Ready to Checkout</div>
                </div>
            </div>
        </div>

        <!-- Right: Shopping Cart Summary -->
        <div class="col-md-4">
            <div class="cart-summary-section">
                <div class="cart-info">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span style="color: #667eea; font-weight: 500;">Items in cart:</span>
                        <span class="badge bg-primary" id="cartItemCount">0</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span style="color: #667eea; font-weight: 500;">Total amount:</span>
                        <span class="fw-bold text-success" style="font-size: 1.3rem;" id="cartTotal">$0.00</span>
                    </div>
                </div>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('customer.cart') }}" class="btn btn-outline-primary btn-sm" id="viewCartBtn">
                        <i class="bi bi-cart"></i> View & Edit Cart
                    </a>
                    <button class="btn btn-success btn-sm" id="proceedCheckout" disabled>
                        <i class="bi bi-credit-card"></i> Proceed to Checkout
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Panel -->
    <div class="status-card" id="statusCard">
        <div id="loadingState" class="text-center">
            <div class="loading-spinner mb-3"></div>
            <h4 style="color: #667eea;">🚀 Getting Everything Ready...</h4>
            <p class="text-muted">Setting up your smart shopping experience</p>
            <div class="progress" style="height: 8px; border-radius: 10px;">
                <div class="progress-bar" role="progressbar" style="width: 0%; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);" id="loadingProgress"></div>
            </div>
        </div>

        <div id="activeState" style="display: none;" class="text-center">
            <div style="color: #28a745; font-size: 1.2rem; font-weight: 600;">
                <i class="bi bi-camera-video"></i> Camera Ready
                <span class="badge bg-success ms-2">LIVE</span>
            </div>
        </div>

        <div id="errorState" style="display: none;" class="text-center">
            <div style="color: #ff6b6b;">
                <i class="bi bi-exclamation-triangle" style="font-size: 4rem; margin-bottom: 20px;"></i>
                <h4>Oops! Something went wrong 😔</h4>
                <p id="errorMessage" class="mb-4">We couldn't start the camera system</p>
                <button class="btn btn-friendly" onclick="retryStart()">
                    <i class="bi bi-arrow-clockwise"></i> Let's Try Again! 🔄
                </button>
            </div>
        </div>
    </div>





    <div class="row justify-content-center">
        <!-- AI Detection Panel (Full Width) -->
        <div class="col-lg-8">
            <div class="detection-panel">
                <h4 class="mb-3 text-center">
                    <i class="bi bi-robot"></i> 🤖 AI Product Detection
                </h4>
                <div id="detectedProducts">
                    <div class="text-center py-4">
                        <div style="font-size: 3rem; margin-bottom: 15px;">👀</div>
                        <h6>Looking for products...</h6>
                        <p class="mb-0" style="opacity: 0.8;">Place items in front of the camera</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Security Monitoring Panel -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="status-card">
                <h5 class="text-center mb-3" style="color: #667eea;">
                    <i class="bi bi-shield-check"></i> 🛡️ Security Monitoring
                    <span class="badge bg-danger ms-2" id="alertCount">0</span>
                </h5>
                <div id="securityAlerts">
                    <div class="text-center text-muted">
                        <div style="font-size: 2rem; margin-bottom: 10px;">✅</div>
                        <p class="mb-0">All good! No security alerts</p>
                        <small>Your shopping is being monitored for safety</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Exit Confirmation Modal -->
<div class="modal fade" id="exitConfirmModal" tabindex="-1" aria-labelledby="exitConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="exitConfirmModalLabel">
                    <i class="bi bi-power"></i> End Shopping Session?
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <i class="bi bi-cart-x text-danger" style="font-size: 4rem;"></i>
                </div>
                <div class="alert alert-warning">
                    <h6 class="alert-heading">
                        <i class="bi bi-exclamation-triangle"></i> This will end your shopping session
                    </h6>
                    <p class="mb-2"><strong>You currently have <span id="modalCartCount">0</span> item(s) in your cart.</strong></p>
                    <hr>
                    <p class="mb-0">The following actions will occur:</p>
                    <ul class="mb-0 mt-2">
                        <li><i class="bi bi-camera-video-off text-danger"></i> Stop camera monitoring</li>
                        <li><i class="bi bi-trash text-danger"></i> Clear all items from your cart</li>
                        <li><i class="bi bi-house text-primary"></i> Return to homepage</li>
                    </ul>
                </div>
                <p class="text-center text-muted mb-0">
                    <strong>Are you sure you want to end your shopping session?</strong>
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" data-bs-dismiss="modal">
                    <i class="bi bi-cart-check"></i> Continue Shopping
                </button>
                <button type="button" class="btn btn-danger" id="confirmExitBtn">
                    <i class="bi bi-power"></i> Yes, End Session
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.badge.bg-success {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.product-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    background: white;
}

.product-item.detected {
    border-color: #007bff;
    background: #f8f9ff;
}

.alert-item {
    border-left: 4px solid #dc3545;
    background: #fff5f5;
    padding: 10px;
    margin-bottom: 8px;
    border-radius: 4px;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let updateInterval;
let isSystemActive = false;
let currentCartCount = 0;
let lastAlertTime = {}; // Track when alerts were last shown for each product
let activeAlerts = {}; // Track currently active persistent alerts

document.addEventListener('DOMContentLoaded', function() {
    // Restore persistent alerts first
    restorePersistentAlerts();

    // Auto-start the camera system
    autoStartShopping();

    // Update cart count
    updateCartCount();



    // Event listeners
    document.getElementById('proceedCheckout').addEventListener('click', proceedToCheckout);
    document.getElementById('backToHomeBtn').addEventListener('click', handleBackToHome);
    document.getElementById('confirmExitBtn').addEventListener('click', confirmExit);
    document.getElementById('addBarcodeBtn').addEventListener('click', addProductByBarcode);
    document.getElementById('barcodeInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            addProductByBarcode();
        }
    });

    // Track if user is navigating to legitimate pages (cart, home, etc.)
    let isLegitimateNavigation = false;

    // Allow navigation to cart, checkout, and home without warning
    document.addEventListener('click', function(e) {
        const target = e.target.closest('a, button');
        if (target) {
            const href = target.getAttribute('href') || target.getAttribute('onclick');
            if (href && (href.includes('/cart') || href.includes('/checkout') || href.includes('/customer') || href.includes('/payment') || href.includes('/'))) {
                console.log('🔄 Legitimate navigation detected:', href);
                isLegitimateNavigation = true;
                // Reset after a short delay
                setTimeout(() => { isLegitimateNavigation = false; }, 500);
            }
        }
    });

    // Only prevent accidental page close/refresh, not legitimate navigation
    window.addEventListener('beforeunload', function(e) {
        // Check if this is a checkout navigation
        const isCheckoutNavigation = sessionStorage.getItem('isCheckoutNavigation') === 'true';

        if (isCheckoutNavigation) {
            console.log('🛒 Checkout navigation detected - allowing without warning');
            return; // Don't show warning
        }

        if (currentCartCount > 0 && !isLegitimateNavigation) {
            console.log('⚠️ Showing beforeunload warning - cart has items');
            e.preventDefault();
            e.returnValue = 'Are you sure you want to end shopping? You have items in your cart.';
            return e.returnValue;
        }
    });
});

function autoStartShopping() {
    console.log('🚀 Auto-starting shopping system...');

    // Animate loading progress
    let progress = 0;
    const progressBar = document.getElementById('loadingProgress');
    const progressInterval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;
        progressBar.style.width = progress + '%';
    }, 200);

    // Start camera automatically
    setTimeout(() => {
        fetch('/customer/api/camera/start-shopping')
            .then(response => response.json())
            .then(data => {
                clearInterval(progressInterval);
                progressBar.style.width = '100%';

                setTimeout(() => {
                    if (data.success) {
                        console.log('✅ Camera started successfully');
                        showActiveState();
                        startDataUpdates();
                        showWelcomeMessage();
                    } else {
                        console.error('❌ Failed to start camera:', data.message);
                        showErrorState(data.message);
                    }
                }, 500);
            })
            .catch(error => {
                clearInterval(progressInterval);
                console.error('❌ Error starting camera:', error);
                showErrorState('Network error - please check your connection');
            });
    }, 1500);
}

function showWelcomeMessage() {
    showFriendlyAlert('success', '🎯 Smart shopping system is ready! Place items in your cart to get started.');
}

// Test function to verify alerts work
function testAlert() {
    showFriendlyAlert('warning', '🧪 TEST: This is a popup alert in the top-right corner!');
}

// Function to show persistent alerts that stay until issue is resolved
function showPersistentAlert(alertKey, type, message) {
    console.log(`🚨 showPersistentAlert called: ${alertKey} - ${type} - ${message}`);

    // If alert already exists, don't create duplicate
    if (activeAlerts[alertKey]) {
        console.log(`⏰ Alert ${alertKey} already active, skipping duplicate`);
        return;
    }

    // Store alert in sessionStorage for cross-page persistence
    const alertData = { alertKey, type, message, timestamp: Date.now() };
    let storedAlerts = JSON.parse(sessionStorage.getItem('persistentAlerts') || '[]');
    storedAlerts = storedAlerts.filter(alert => alert.alertKey !== alertKey); // Remove duplicates
    storedAlerts.push(alertData);
    sessionStorage.setItem('persistentAlerts', JSON.stringify(storedAlerts));

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.id = `persistent-alert-${alertKey}`;
    alertDiv.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 350px;
        max-width: 450px;
        border-radius: 15px;
        border: none;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        margin-bottom: 10px;
    `;

    // Set background based on type
    let bgColor = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
    if (type === 'success') bgColor = 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)';
    if (type === 'warning') bgColor = 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)';
    if (type === 'danger') bgColor = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';

    alertDiv.style.background = bgColor;
    alertDiv.style.color = 'white';

    // Add pulsing animation for warnings and dangers
    if (type === 'warning' || type === 'danger') {
        alertDiv.style.animation = 'alertPulse 2s ease-in-out infinite';
    }

    alertDiv.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="flex-grow-1">${message}</div>
            <button type="button" class="btn-close btn-close-white" onclick="dismissPersistentAlert('${alertKey}')"></button>
        </div>
    `;

    // Position multiple alerts
    const existingAlerts = document.querySelectorAll('[id^="persistent-alert-"]');
    if (existingAlerts.length > 0) {
        alertDiv.style.top = `${20 + (existingAlerts.length * 80)}px`;
    }

    document.body.appendChild(alertDiv);
    activeAlerts[alertKey] = alertDiv;

    console.log(`✅ Persistent alert ${alertKey} created and stored`);
}

// Function to dismiss a specific persistent alert
function dismissPersistentAlert(alertKey) {
    console.log(`🗑️ Dismissing persistent alert: ${alertKey}`);

    if (activeAlerts[alertKey]) {
        activeAlerts[alertKey].remove();
        delete activeAlerts[alertKey];
        console.log(`✅ Alert ${alertKey} dismissed and removed`);

        // Remove from sessionStorage
        let storedAlerts = JSON.parse(sessionStorage.getItem('persistentAlerts') || '[]');
        storedAlerts = storedAlerts.filter(alert => alert.alertKey !== alertKey);
        sessionStorage.setItem('persistentAlerts', JSON.stringify(storedAlerts));

        // Reposition remaining alerts
        repositionAlerts();
    }
}

// Function to reposition alerts after one is removed
function repositionAlerts() {
    const remainingAlerts = document.querySelectorAll('[id^="persistent-alert-"]');
    remainingAlerts.forEach((alert, index) => {
        alert.style.top = `${20 + (index * 80)}px`;
    });
}

// Function to restore persistent alerts from sessionStorage
function restorePersistentAlerts() {
    const storedAlerts = JSON.parse(sessionStorage.getItem('persistentAlerts') || '[]');
    console.log(`🔄 Restoring ${storedAlerts.length} persistent alerts from storage`);

    storedAlerts.forEach(alertData => {
        // Only restore if not already active
        if (!activeAlerts[alertData.alertKey]) {
            showPersistentAlert(alertData.alertKey, alertData.type, alertData.message);
        }
    });
}

// Function to check if there are active alerts (global function)
function hasActiveAlerts() {
    const storedAlerts = JSON.parse(sessionStorage.getItem('persistentAlerts') || '[]');
    return storedAlerts.length > 0;
}

// Make function globally available
window.hasActiveAlerts = hasActiveAlerts;

function showFriendlyAlert(type, message) {
    console.log(`🚨 showFriendlyAlert called: ${type} - ${message}`);
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 350px;
        max-width: 450px;
        border-radius: 15px;
        border: none;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
    `;

    // Set background based on type
    let bgColor = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
    if (type === 'success') bgColor = 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)';
    if (type === 'warning') bgColor = 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)';
    if (type === 'danger') bgColor = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';

    alertDiv.style.background = bgColor;
    alertDiv.style.color = 'white';

    if (type === 'warning' || type === 'danger') {
        alertDiv.style.animation = 'alertPulse 0.5s ease-in-out 3';
    }

    alertDiv.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="flex-grow-1">${message}</div>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert"></button>
        </div>
    `;

    document.body.appendChild(alertDiv);

    const timeout = (type === 'warning' || type === 'danger') ? 6000 : 4000;
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, timeout);
}

function showActiveState() {
    document.getElementById('loadingState').style.display = 'none';
    document.getElementById('errorState').style.display = 'none';
    document.getElementById('activeState').style.display = 'block';
    isSystemActive = true;
}

function showErrorState(message) {
    document.getElementById('loadingState').style.display = 'none';
    document.getElementById('activeState').style.display = 'none';
    document.getElementById('errorState').style.display = 'block';
    document.getElementById('errorMessage').textContent = message;
    isSystemActive = false;
}

function retryStart() {
    document.getElementById('errorState').style.display = 'none';
    document.getElementById('loadingState').style.display = 'block';
    setTimeout(autoStartShopping, 1000);
}

function startDataUpdates() {
    if (updateInterval) return;
    updateInterval = setInterval(updateShoppingData, 2000);
    updateShoppingData();
}

function updateShoppingData() {
    if (!isSystemActive) return;

    fetch('/customer/api/camera/detected-products')
        .then(response => response.json())
        .then(data => {
            console.log('📊 Shopping data received:', data);
            if (data.success) {
                updateDetectedProducts(data.detected_products);
                updateCartDisplay(data.cart_count, data.cart_total);
                checkForAlerts(data.detected_products, data.cart_products);

                // Update progress steps based on detected products
                const detectedCount = Object.keys(data.detected_products).length;
                updateProgressSteps(detectedCount);
            }
        })
        .catch(error => console.error('Error updating data:', error));

    // Update security alerts
    fetch('/customer/api/camera/shopping-status')
        .then(response => response.json())
        .then(data => {
            updateSecurityAlerts(data.recent_alarms || []);
        })
        .catch(error => console.error('Error updating alerts:', error));
}

function updateDetectedProducts(detectedProducts) {
    const container = document.getElementById('detectedProducts');
    const totalCount = Object.values(detectedProducts).reduce((sum, count) => sum + count, 0);

    // Update counter with animation (if element exists)
    const counter = document.getElementById('detectedCount');
    if (counter) {
        if (counter.textContent !== totalCount.toString()) {
            counter.classList.add('celebration');
            setTimeout(() => counter.classList.remove('celebration'), 600);
        }
        counter.textContent = totalCount;
    }

    // Update progress steps
    updateProgressSteps(totalCount);

    if (Object.keys(detectedProducts).length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <div style="font-size: 3rem; margin-bottom: 15px;">👀</div>
                <h6>Looking for products...</h6>
                <p class="mb-0" style="opacity: 0.8;">Place items in front of the camera</p>
            </div>
        `;
    } else {
        let html = '';
        for (const [product, count] of Object.entries(detectedProducts)) {
            html += `
                <div class="product-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1" style="color: #333;">
                                🛍️ ${product}
                            </h6>
                            <small style="color: #666;">
                                <i class="bi bi-robot"></i> Detected by AI
                            </small>
                        </div>
                        <div class="text-center">
                            <div class="badge" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                                ${count} found
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        container.innerHTML = html;

        // Note: Removed automatic celebration alert to prevent spam
    }
}

function updateProgressSteps(detectedCount) {
    console.log(`🔄 Updating progress steps - detected: ${detectedCount}, cart: ${currentCartCount}`);

    const step2 = document.getElementById('step2');
    const step3 = document.getElementById('step3');

    if (detectedCount > 0) {
        console.log('✅ Step 2 (Detecting Products) - ACTIVE');
        step2.classList.add('active');
        if (currentCartCount > 0) {
            console.log('✅ Step 3 (Ready to Checkout) - ACTIVE');
            step3.classList.add('active');
        } else {
            console.log('⏸️ Step 3 (Ready to Checkout) - INACTIVE (no items in cart)');
            step3.classList.remove('active');
        }
    } else {
        console.log('⏸️ Step 2 (Detecting Products) - INACTIVE (no items detected)');
        console.log('⏸️ Step 3 (Ready to Checkout) - INACTIVE (no items detected)');
        step2.classList.remove('active');
        step3.classList.remove('active');
    }
}



function updateCartDisplay(cartCount, cartTotal) {
    // Update global cart count
    currentCartCount = cartCount;

    // Safely update elements (with null checks)
    const cartTotal_el = document.getElementById('cartTotal');
    if (cartTotal_el) cartTotal_el.textContent = `$${cartTotal.toFixed(2)}`;

    const cartItemCount_el = document.getElementById('cartItemCount');
    if (cartItemCount_el) cartItemCount_el.textContent = cartCount;

    const cartCount_el = document.getElementById('cartCount');
    if (cartCount_el) cartCount_el.textContent = cartCount;

    const checkoutBtn = document.getElementById('proceedCheckout');
    if (checkoutBtn) checkoutBtn.disabled = cartCount === 0;

    const viewCartBtn = document.getElementById('viewCartBtn');
    if (viewCartBtn) {
        if (cartCount > 0) {
            viewCartBtn.innerHTML = `<i class="bi bi-cart"></i> View Cart (${cartCount} items)`;
        } else {
            viewCartBtn.innerHTML = `<i class="bi bi-cart"></i> View & Edit Cart`;
        }
    }

    // Update back button style based on cart status
    const backBtn = document.getElementById('backToHomeBtn');
    if (backBtn) {
        if (cartCount > 0) {
            backBtn.className = 'btn btn-warning';
            backBtn.innerHTML = '<i class="bi bi-arrow-left"></i> Back to Home';
        } else {
            backBtn.className = 'btn btn-outline-secondary';
            backBtn.innerHTML = '<i class="bi bi-arrow-left"></i> Back to Home';
        }
    }
}

function checkForAlerts(detectedProducts, cartProducts) {
    console.log('🔍 Checking for alerts...');
    console.log('Detected products:', detectedProducts);
    console.log('Cart products:', cartProducts);

    // Check for physically detected items that aren't scanned
    for (const [product, detectedCount] of Object.entries(detectedProducts)) {
        const cartCount = cartProducts[product] || 0;
        console.log(`🔍 AUTO-START PAGE - Checking product: ${product}, detected: ${detectedCount}, cart: ${cartCount}`);

        const unscannedAlertKey = `unscanned_${product}`;
        const extraAlertKey = `extra_${product}`;
        const overstockedAlertKey = `overstocked_${product}`;

        if (cartCount === 0 && detectedCount > 0) {
            // Show persistent alert for unscanned item
            console.log(`⚠️ AUTO-START PAGE - ALERT TRIGGERED: Unscanned product ${product} detected`);
            showPersistentAlert(unscannedAlertKey, 'warning',
                `🛍️ Hey! I found ${product} in your cart, but it's not scanned yet. Don't forget to add it! 😊`);
        } else if (detectedCount > cartCount && cartCount > 0) {
            // Show persistent alert for extra items
            console.log(`⚠️ AUTO-START PAGE - ALERT TRIGGERED: Extra ${product} detected - scanned: ${cartCount}, detected: ${detectedCount}`);
            showPersistentAlert(extraAlertKey, 'warning',
                `🔍 I see ${detectedCount} ${product} but only ${cartCount} scanned. Please check your cart! 📝`);
        } else if (cartCount > detectedCount && cartCount > 0) {
            // Show alert for cart having more than detected
            console.log(`⚠️ AUTO-START PAGE - ALERT TRIGGERED: Cart has more ${product} than detected - cart: ${cartCount}, detected: ${detectedCount}`);
            showPersistentAlert(overstockedAlertKey, 'danger',
                `📦 Your cart shows ${cartCount} ${product} but I only see ${detectedCount}. Please add ${cartCount - detectedCount} more or remove ${cartCount - detectedCount} from cart! 🔄`);
        } else {
            // Issue resolved - dismiss alerts for this product
            console.log(`✅ AUTO-START PAGE - No alert needed for ${product} - counts match`);
            dismissPersistentAlert(unscannedAlertKey);
            dismissPersistentAlert(extraAlertKey);
            dismissPersistentAlert(overstockedAlertKey);
        }
    }

    // IMPORTANT: Also check for products that are in cart but not detected at all
    for (const [product, cartCount] of Object.entries(cartProducts)) {
        if (cartCount > 0 && !detectedProducts.hasOwnProperty(product)) {
            const detectedCount = 0;
            console.log(`🔍 AUTO-START PAGE - Checking cart-only product: ${product}, cart: ${cartCount}, detected: ${detectedCount}`);

            const overstockedAlertKey = `overstocked_${product}`;
            console.log(`⚠️ AUTO-START PAGE - ALERT TRIGGERED: Cart has ${product} but none detected - cart: ${cartCount}, detected: ${detectedCount}`);
            showPersistentAlert(overstockedAlertKey, 'danger',
                `📦 Your cart shows ${cartCount} ${product} but I don't see any. Please add ${cartCount} more or remove them from cart! 🔄`);
        }
    }

    // Check for scanned items that aren't physically present
    for (const [product, cartCount] of Object.entries(cartProducts)) {
        const detectedCount = detectedProducts[product] || 0;
        console.log(`🔍 Checking cart product: ${product}, cart: ${cartCount}, detected: ${detectedCount}`);

        const removedAlertKey = `removed_${product}`;

        if (cartCount > 0 && detectedCount === 0) {
            // Show persistent alert for removed item
            console.log(`⚠️ ALERT TRIGGERED: Product ${product} removed from cart`);
            showPersistentAlert(removedAlertKey, 'danger',
                `👀 Looks like ${product} was removed from your cart. Just making sure that's intentional! 🛒`);
        } else {
            // Issue resolved - dismiss alert for this product
            console.log(`✅ No removal alert needed for ${product}`);
            dismissPersistentAlert(removedAlertKey);
        }
    }

    // Clean up alerts for products that are no longer detected AND not in cart
    for (const alertKey in activeAlerts) {
        const productName = alertKey.replace(/^(unscanned_|extra_|removed_|overstocked_)/, '');
        const isDetected = detectedProducts.hasOwnProperty(productName);
        const isInCart = cartProducts.hasOwnProperty(productName);

        console.log(`🧹 Cleanup check for ${alertKey}: product=${productName}, detected=${isDetected}, inCart=${isInCart}`);

        // Only clean up if product is completely gone (not detected AND not in cart)
        if (!isDetected && !isInCart) {
            console.log(`🧹 Cleaning up alert for product completely gone: ${productName}`);
            dismissPersistentAlert(alertKey);
        } else {
            console.log(`✅ Keeping alert ${alertKey} - product still relevant`);
        }
    }

    console.log('🔍 Alert check completed');
}

function updateSecurityAlerts(alerts) {
    const container = document.getElementById('securityAlerts');
    const alertCount = document.getElementById('alertCount');

    alertCount.textContent = alerts.length;

    if (alerts.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted">
                <div style="font-size: 2rem; margin-bottom: 10px;">✅</div>
                <p class="mb-0">All good! No security alerts</p>
                <small>Your shopping is being monitored for safety</small>
            </div>
        `;
    } else {
        let html = '';
        alerts.forEach(alert => {
            html += `
                <div class="security-alert">
                    <div class="d-flex justify-content-between align-items-center">
                        <span><i class="bi bi-exclamation-triangle"></i> ${alert.message}</span>
                        <small>${alert.timestamp}</small>
                    </div>
                </div>
            `;
        });
        container.innerHTML = html;
    }
}



function updateCartCount() {
    fetch('/customer/get-cart-count')
        .then(response => response.json())
        .then(data => {
            currentCartCount = data.count;
            document.getElementById('cartCount').textContent = data.count;
        })
        .catch(error => console.error('Error updating cart count:', error));
}

function handleBackToHome() {
    console.log('🏠 Back to home clicked, cart count:', currentCartCount);

    if (currentCartCount > 0) {
        // Show confirmation modal if cart has items
        document.getElementById('modalCartCount').textContent = currentCartCount;
        const modal = new bootstrap.Modal(document.getElementById('exitConfirmModal'));
        modal.show();
    } else {
        // Go directly to homepage if cart is empty
        goToHomepage();
    }
}

function confirmExit() {
    // User confirmed they want to leave
    const modal = bootstrap.Modal.getInstance(document.getElementById('exitConfirmModal'));
    modal.hide();
    goToHomepage();
}

function goToHomepage() {
    console.log('🏠 Ending shopping session and going to homepage...');

    // Show loading state
    showAlert('info', '🔄 Ending shopping session...');

    // Step 1: Clear the cart
    fetch('/customer/clear-cart', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('🗑️ Cart cleared:', data.message);

        // Step 2: Stop the camera system
        if (isSystemActive) {
            return fetch('/customer/api/camera/stop-shopping');
        } else {
            return Promise.resolve({json: () => ({success: true, message: 'Camera already stopped'})});
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('📷 Camera stopped:', data.message);

        // Step 3: Show success message and navigate
        showAlert('success', '✅ Shopping session ended successfully!');

        // Navigate to homepage after a short delay
        setTimeout(() => {
            window.location.href = '/';
        }, 1500);
    })
    .catch(error => {
        console.error('❌ Error ending session:', error);
        showAlert('danger', '❌ Error ending session, but navigating to homepage...');

        // Navigate anyway after error
        setTimeout(() => {
            window.location.href = '/';
        }, 2000);
    });
}

function proceedToCheckout() {
    window.location.href = '/customer/cart';
}

function addProductByBarcode() {
    const barcodeInput = document.getElementById('barcodeInput');
    const barcode = barcodeInput.value.trim();

    if (!barcode) {
        showFriendlyAlert('warning', '📱 Please enter a barcode first!');
        barcodeInput.focus();
        return;
    }

    // Show loading state
    const addBtn = document.getElementById('addBarcodeBtn');
    const originalText = addBtn.innerHTML;
    addBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Adding...';
    addBtn.disabled = true;

    // Add product via barcode
    fetch('/customer/scan', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `barcode=${encodeURIComponent(barcode)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Success - clear input and show success message
            barcodeInput.value = '';
            showFriendlyAlert('success', `🎉 ${data.product.name} added to your cart!`);

            // Update cart count and display
            updateCartCount();

        } else {
            // Error - show error message
            showFriendlyAlert('danger', `❌ ${data.error || data.message || 'Unknown error'}`);
            barcodeInput.focus();
            barcodeInput.select();
        }
    })
    .catch(error => {
        console.error('Error adding product:', error);
        showFriendlyAlert('danger', '❌ Network error. Please try again!');
        barcodeInput.focus();
    })
    .finally(() => {
        // Restore button state
        addBtn.innerHTML = originalText;
        addBtn.disabled = false;
    });
}

// Auto-focus barcode input when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Focus navigation barcode input after auto-start completes
    setTimeout(() => {
        const barcodeInput = document.getElementById('barcodeInput');
        if (barcodeInput) {
            barcodeInput.focus();
            // Add a subtle highlight effect to the navigation input
            barcodeInput.style.borderColor = '#4facfe';
            barcodeInput.style.boxShadow = '0 0 0 0.2rem rgba(79,172,254,0.25)';
            setTimeout(() => {
                barcodeInput.style.borderColor = '#e0e0e0';
                barcodeInput.style.boxShadow = '';
            }, 2000);
        }
    }, 4000); // Wait a bit longer for the system to fully load
});

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = `
        top: 20px; 
        right: 20px; 
        z-index: 9999; 
        min-width: 400px; 
        max-width: 500px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        border: 2px solid;
        font-weight: 500;
    `;
    
    if (type === 'warning' || type === 'danger') {
        alertDiv.style.animation = 'pulse 0.5s ease-in-out 3';
    }
    
    alertDiv.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="me-3">
                ${type === 'warning' ? '<i class="bi bi-exclamation-triangle-fill" style="font-size: 1.5rem;"></i>' : 
                  type === 'danger' ? '<i class="bi bi-shield-exclamation" style="font-size: 1.5rem;"></i>' : 
                  '<i class="bi bi-info-circle-fill" style="font-size: 1.5rem;"></i>'}
            </div>
            <div class="flex-grow-1">${message}</div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.body.appendChild(alertDiv);
    
    const timeout = (type === 'warning' || type === 'danger') ? 8000 : 5000;
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, timeout);
}
</script>
{% endblock %}
