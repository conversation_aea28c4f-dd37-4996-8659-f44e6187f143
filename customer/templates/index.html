{% extends "base.html" %}

{% block title %}Product Scanner{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <a href="/" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Welcome
        </a>
        <h1 class="mb-0 text-center flex-grow-1">Product Scanner</h1>
        <a href="{{ url_for('customer.cart') }}" class="btn btn-primary">
            <i class="bi bi-cart"></i> View Cart <span class="badge bg-light text-dark" id="headerCartCount">0</span>
        </a>
    </div>

    <!-- Instructions Card -->
    <div class="row justify-content-center mb-4">
        <div class="col-md-8">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <h5 class="card-title text-primary mb-4">
                        <i class="bi bi-info-circle-fill me-2"></i>
                        How to Scan Products
                    </h5>
                    <div class="row g-4">
                        <div class="col-12 col-sm-6">
                            <div class="d-flex align-items-start">
                                <div class="bg-primary bg-opacity-10 p-3 rounded-circle me-3">
                                    <i class="bi bi-upc-scan text-primary fs-4"></i>
                                </div>
                                <div>
                                    <h6 class="mb-2">Scan Barcode</h6>
                                    <p class="text-muted mb-0">Place the barcode in front of the scanner</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-sm-6">
                            <div class="d-flex align-items-start">
                                <div class="bg-primary bg-opacity-10 p-3 rounded-circle me-3">
                                    <i class="bi bi-keyboard text-primary fs-4"></i>
                                </div>
                                <div>
                                    <h6 class="mb-2">Manual Entry</h6>
                                    <p class="text-muted mb-0">Or type the barcode number below</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-sm-6">
                            <div class="d-flex align-items-start">
                                <div class="bg-primary bg-opacity-10 p-3 rounded-circle me-3">
                                    <i class="bi bi-cart-plus text-primary fs-4"></i>
                                </div>
                                <div>
                                    <h6 class="mb-2">Auto-Add</h6>
                                    <p class="text-muted mb-0">Products automatically added to cart</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-sm-6">
                            <div class="d-flex align-items-start">
                                <div class="bg-primary bg-opacity-10 p-3 rounded-circle me-3">
                                    <i class="bi bi-sliders text-primary fs-4"></i>
                                </div>
                                <div>
                                    <h6 class="mb-2">Adjust Quantity</h6>
                                    <p class="text-muted mb-0">Modify amounts in cart later</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Barcode Scanner Section -->
    <div class="row justify-content-center mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <form id="scanForm" class="text-center">
                        <div class="mb-3">
                            <label for="barcode" class="form-label">Enter Barcode</label>
                            <div class="input-group mb-3">
                                <input type="text" class="form-control form-control-lg text-center" 
                                       id="barcode" placeholder="Scan or enter barcode here" autofocus>
                                <button type="button" class="btn btn-outline-secondary" onclick="clearInput()">
                                    <i class="bi bi-x-lg"></i>
                                </button>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi bi-upc-scan"></i> Add Product
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Added Alert -->
    <div id="productAlert" class="alert alert-success alert-dismissible fade" role="alert" style="display: none;">
        <span id="alertMessage"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <!-- Last Added Product -->
    <div id="lastAddedProduct" class="row justify-content-center" style="display: none;">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title mb-3">Last Added Product</h5>
                    <img id="productImage" src="" alt="Product Image" class="img-fluid mb-3" style="max-height: 200px;">
                    <h6 id="productName" class="mb-2"></h6>
                    <p id="productPrice" class="text-primary mb-2"></p>
                    <div class="d-flex justify-content-center gap-2">
                        <button onclick="addMore()" class="btn btn-outline-primary">
                            <i class="bi bi-plus"></i> Add Another
                        </button>
                        <a href="{{ url_for('customer.cart') }}" class="btn btn-primary">
                            <i class="bi bi-cart"></i> View Cart
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let lastScannedBarcode = '';

// Update cart count on page load
window.addEventListener('load', function() {
    updateCartCount();
});

// Function to update cart count
function updateCartCount() {
    fetch('/customer/get-cart-count')
    .then(response => response.json())
    .then(data => {
        const cartCount = data.count;
        document.getElementById('headerCartCount').textContent = cartCount;
    })
    .catch(error => console.error('Error updating cart count:', error));
}

document.getElementById('scanForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const barcode = document.getElementById('barcode').value.trim();
    if (barcode) {
        lastScannedBarcode = barcode;
        scanProduct(barcode);
        document.getElementById('barcode').value = '';
    }
});

document.getElementById('barcode').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        e.preventDefault(); // Prevent default form submission
        const barcode = this.value.trim();
        if (barcode) {
            lastScannedBarcode = barcode;
            scanProduct(barcode);
            this.value = '';
        }
    }
});

function clearInput() {
    document.getElementById('barcode').value = '';
    document.getElementById('barcode').focus();
}

function addMore() {
    if (lastScannedBarcode) {
        scanProduct(lastScannedBarcode);
    }
}

function scanProduct(barcode) {
    fetch('/customer/scan', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `barcode=${encodeURIComponent(barcode)}`
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // Update cart count
            updateCartCount();
            
            // Show success message
            showAlert('success', `${data.product.name} has been added to cart`);
            
            // Update last added product
            document.getElementById('lastAddedProduct').style.display = 'flex';
            document.getElementById('productName').textContent = data.product.name;
            document.getElementById('productPrice').textContent = `$${data.product.price.toFixed(2)}`;
            
            if (data.product.image_path) {
                document.getElementById('productImage').src = '/' + data.product.image_path;
            } else {
                document.getElementById('productImage').src = '/static/images/no-image.png';
            }
        } else {
            showAlert('danger', data.message || 'Error adding product to cart');
        }
        // Focus back on input for next scan
        document.getElementById('barcode').focus();
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'Error scanning product. Please try again.');
    });
}

function showAlert(type, message) {
    const alert = document.getElementById('productAlert');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    document.getElementById('alertMessage').textContent = message;
    alert.style.display = 'block';
    
    // Auto hide after 3 seconds
    setTimeout(() => {
        alert.style.display = 'none';
    }, 3000);
}
</script>
{% endblock %} 