{% extends "base.html" %}

{% block title %}Shopping Cart{% endblock %}

{% block extra_css %}
<style>
@keyframes alertPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 15px 40px rgba(0,0,0,0.3);
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0">
            <i class="bi bi-cart"></i> Shopping Cart
        </h1>
        <a href="{{ url_for('customer.auto_start_shopping') }}" class="btn btn-outline-secondary">
            <i class="bi bi-camera"></i> Back to Scanning
        </a>
    </div>

    <!-- Server-side checkout error message -->
    {% if session.checkout_error %}
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-shield-exclamation"></i>
        <strong>{{ session.checkout_error }}</strong>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% set _ = session.pop('checkout_error', None) %}
    {% endif %}

    {% if cart %}
        <div class="row">
            <div class="col-lg-8">
                <!-- Shopping Cart Items -->
                <div class="card mb-4">
                    <div class="card-body">
                        {% for item in cart %}
                            <div class="row align-items-center mb-3">
                                <div class="col-md-2">
                                    {% if item.image_path %}
                                        <img src="/{{ item.image_path }}" alt="{{ item.name }}" class="img-fluid rounded">
                                    {% else %}
                                        <div class="no-image-placeholder rounded">
                                            <i class="bi bi-image"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4">
                                    <h5 class="mb-1">{{ item.name }}</h5>
                                    <p class="text-muted mb-0">${{ "%.2f"|format(item.price) }}</p>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center">
                                        <a href="{{ url_for('customer.update_cart_item', product_id=item.id, action='decrease') }}" 
                                           class="btn btn-outline-secondary btn-sm">
                                            <i class="bi bi-dash"></i>
                                        </a>
                                        <span class="mx-3">{{ item.quantity }}</span>
                                        <a href="{{ url_for('customer.update_cart_item', product_id=item.id, action='increase') }}" 
                                           class="btn btn-outline-secondary btn-sm">
                                            <i class="bi bi-plus"></i>
                                        </a>
                                    </div>
                                </div>
                                <div class="col-md-2 text-end">
                                    <a href="{{ url_for('customer.remove_from_cart', product_id=item.id) }}" 
                                       class="btn btn-danger btn-sm">
                                        <i class="bi bi-trash"></i>
                                    </a>
                                </div>
                            </div>
                            {% if not loop.last %}
                                <hr>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <!-- Order Summary -->
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title mb-4">Order Summary</h5>
                        <div class="d-flex justify-content-between mb-3">
                            <span>Number of Items:</span>
                            <span>{{ cart | map(attribute='quantity') | sum }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-3">
                            <span>Subtotal:</span>
                            <span>${{ "%.2f"|format(total) }}</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <strong>Total:</strong>
                            <strong>${{ "%.2f"|format(total) }}</strong>
                        </div>
                        <a href="{{ url_for('customer.checkout') }}" class="btn btn-primary w-100">
                            <i class="bi bi-credit-card"></i> Proceed to Checkout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    {% else %}
        <div class="text-center py-5">
            <i class="bi bi-cart-x display-1 text-muted"></i>
            <h2 class="mt-3">Your Cart is Empty</h2>
            <p class="text-muted">You haven't added any products to your cart yet.</p>
            <a href="{{ url_for('customer.auto_start_shopping') }}" class="btn btn-primary mt-3">
                <i class="bi bi-cart-plus"></i> Start Shopping
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
let activeAlerts = {}; // Track currently active persistent alerts

// Function to show persistent alerts that stay until issue is resolved
function showPersistentAlert(alertKey, type, message) {
    console.log(`🚨 Cart page - showPersistentAlert called: ${alertKey} - ${type} - ${message}`);

    // If alert already exists, don't create duplicate
    if (activeAlerts[alertKey]) {
        console.log(`⏰ Alert ${alertKey} already active, skipping duplicate`);
        return;
    }

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.id = `persistent-alert-${alertKey}`;
    alertDiv.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 350px;
        max-width: 450px;
        border-radius: 15px;
        border: none;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        margin-bottom: 10px;
    `;

    // Set background based on type
    let bgColor = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
    if (type === 'success') bgColor = 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)';
    if (type === 'warning') bgColor = 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)';
    if (type === 'danger') bgColor = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';

    alertDiv.style.background = bgColor;
    alertDiv.style.color = 'white';

    // Add pulsing animation for warnings and dangers
    if (type === 'warning' || type === 'danger') {
        alertDiv.style.animation = 'alertPulse 2s ease-in-out infinite';
    }

    alertDiv.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="flex-grow-1">${message}</div>
            <div class="ms-2">
                <small style="opacity: 0.8;">Go back to scanning to resolve</small>
            </div>
        </div>
    `;

    // Position multiple alerts
    const existingAlerts = document.querySelectorAll('[id^="persistent-alert-"]');
    if (existingAlerts.length > 0) {
        alertDiv.style.top = `${20 + (existingAlerts.length * 80)}px`;
    }

    document.body.appendChild(alertDiv);
    activeAlerts[alertKey] = alertDiv;

    console.log(`✅ Persistent alert ${alertKey} created and stored on cart page`);
}

// Function to restore persistent alerts from sessionStorage
function restorePersistentAlerts() {
    const storedAlerts = JSON.parse(sessionStorage.getItem('persistentAlerts') || '[]');
    console.log(`🔄 Cart page - Restoring ${storedAlerts.length} persistent alerts from storage`);

    storedAlerts.forEach(alertData => {
        // Only restore if not already active
        if (!activeAlerts[alertData.alertKey]) {
            showPersistentAlert(alertData.alertKey, alertData.type, alertData.message);
        }
    });
}

// Function to check if there are active alerts (for blocking checkout)
function hasActiveAlerts() {
    const storedAlerts = JSON.parse(sessionStorage.getItem('persistentAlerts') || '[]');
    const activeAlertsCount = Object.keys(activeAlerts).length;
    const sessionAlertsCount = storedAlerts.length;

    console.log(`🔍 hasActiveAlerts check: activeAlerts=${activeAlertsCount}, sessionStorage=${sessionAlertsCount}`);
    console.log('🔍 Active alerts object:', Object.keys(activeAlerts));
    console.log('🔍 Session storage alerts:', storedAlerts.map(a => a.alertKey));

    return sessionAlertsCount > 0 || activeAlertsCount > 0;
}

// Function to block checkout if alerts are active
function checkCheckoutAllowed() {
    const checkoutBtn = document.querySelector('a[href*="checkout"]');
    if (checkoutBtn) {
        if (hasActiveAlerts()) {
            // Store original href if not already stored
            if (!checkoutBtn.dataset.originalHref) {
                checkoutBtn.dataset.originalHref = checkoutBtn.href;
            }

            // Completely disable the button
            checkoutBtn.href = '#';
            checkoutBtn.style.pointerEvents = 'auto'; // Allow clicks to show alert
            checkoutBtn.style.opacity = '0.5';
            checkoutBtn.style.cursor = 'not-allowed';
            checkoutBtn.title = 'Please resolve all security alerts before checkout';
            checkoutBtn.classList.add('disabled');

            // Remove any existing click handlers and add blocking handler
            checkoutBtn.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();
                alert('🚫 Checkout Blocked!\n\nPlease return to the scanning page and resolve all security alerts before proceeding to checkout.');
                return false;
            };

            // Add warning message
            let warningMsg = document.getElementById('checkout-warning');
            if (!warningMsg) {
                warningMsg = document.createElement('div');
                warningMsg.id = 'checkout-warning';
                warningMsg.className = 'alert alert-warning mt-2';
                warningMsg.innerHTML = `
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Checkout Blocked:</strong> Please return to scanning and resolve all security alerts before proceeding.
                `;
                checkoutBtn.parentNode.insertBefore(warningMsg, checkoutBtn);
            }
        } else {
            // Restore original functionality
            if (checkoutBtn.dataset.originalHref) {
                checkoutBtn.href = checkoutBtn.dataset.originalHref;
            } else {
                // Ensure href is set to checkout URL
                checkoutBtn.href = '/customer/checkout';
            }
            checkoutBtn.style.pointerEvents = 'auto';
            checkoutBtn.style.opacity = '1';
            checkoutBtn.style.cursor = 'pointer';
            checkoutBtn.title = '';
            checkoutBtn.classList.remove('disabled');
            checkoutBtn.onclick = null; // Remove blocking handler

            console.log('✅ Checkout button restored to normal functionality');

            // Remove warning message
            const warningMsg = document.getElementById('checkout-warning');
            if (warningMsg) {
                warningMsg.remove();
            }
        }
    }
}

// Function to check for alerts by comparing detected vs cart products
function checkForAlerts() {
    console.log('🔍 Cart page - Checking for alerts...');

    // Fetch current detection and cart data
    Promise.all([
        fetch('/customer/api/camera/detected-products').then(r => r.json()),
        fetch('/customer/get-cart-count').then(r => r.json())
    ]).then(([detectionData, cartData]) => {
        if (detectionData.success) {
            const detectedProducts = detectionData.detected_products;
            const cartProducts = detectionData.cart_products;

            console.log('🔍 Cart page - Detected products:', detectedProducts);
            console.log('🔍 Cart page - Cart products:', cartProducts);

            // Check for physically detected items that aren't scanned
            for (const [product, detectedCount] of Object.entries(detectedProducts)) {
                const cartCount = cartProducts[product] || 0;
                console.log(`🔍 Cart page - Checking product: ${product}, detected: ${detectedCount}, cart: ${cartCount}`);

                const unscannedAlertKey = `unscanned_${product}`;
                const extraAlertKey = `extra_${product}`;

                if (cartCount === 0 && detectedCount > 0) {
                    // Show persistent alert for unscanned item
                    console.log(`⚠️ Cart page - ALERT TRIGGERED: Unscanned product ${product} detected`);
                    showPersistentAlert(unscannedAlertKey, 'warning',
                        `🛍️ Hey! I found ${product} in your cart, but it's not scanned yet. Don't forget to add it! 😊`);
                } else if (detectedCount > cartCount) {
                    // Show persistent alert for extra items
                    console.log(`⚠️ Cart page - ALERT TRIGGERED: Extra ${product} detected - scanned: ${cartCount}, detected: ${detectedCount}`);
                    showPersistentAlert(extraAlertKey, 'warning',
                        `🔍 I see ${detectedCount} ${product} but only ${cartCount} scanned. Please check your cart! 📝`);
                } else if (cartCount > detectedCount) {
                    // Show alert for cart having more than detected
                    console.log(`⚠️ Cart page - ALERT TRIGGERED: Cart has more ${product} than detected - cart: ${cartCount}, detected: ${detectedCount}`);
                    showPersistentAlert(`overstocked_${product}`, 'danger',
                        `📦 Your cart shows ${cartCount} ${product} but I only see ${detectedCount}. Please add ${cartCount - detectedCount} more or remove ${cartCount - detectedCount} from cart! 🔄`);
                } else {
                    // Issue resolved - dismiss alerts for this product
                    console.log(`✅ Cart page - No alert needed for ${product} - counts match`);
                    dismissPersistentAlert(unscannedAlertKey);
                    dismissPersistentAlert(extraAlertKey);
                    dismissPersistentAlert(`overstocked_${product}`);
                }
            }

            // Check for scanned items that aren't physically present
            for (const [product, cartCount] of Object.entries(cartProducts)) {
                const detectedCount = detectedProducts[product] || 0;
                console.log(`🔍 Cart page - Checking cart product: ${product}, cart: ${cartCount}, detected: ${detectedCount}`);

                const removedAlertKey = `removed_${product}`;

                if (cartCount > 0 && detectedCount === 0) {
                    // Show persistent alert for removed item
                    console.log(`⚠️ Cart page - ALERT TRIGGERED: Product ${product} removed from cart`);
                    showPersistentAlert(removedAlertKey, 'danger',
                        `👀 Looks like ${product} was removed from your cart. Just making sure that's intentional! 🛒`);
                } else {
                    // Issue resolved - dismiss alert for this product
                    console.log(`✅ Cart page - No removal alert needed for ${product}`);
                    dismissPersistentAlert(removedAlertKey);
                }
            }

            console.log('🔍 Cart page - Alert check completed');
        }
    }).catch(error => {
        console.error('❌ Cart page - Error checking for alerts:', error);
    });
}

// Function to dismiss a specific persistent alert (cart page version)
function dismissPersistentAlert(alertKey) {
    console.log(`🗑️ Cart page - Dismissing persistent alert: ${alertKey}`);

    if (activeAlerts[alertKey]) {
        activeAlerts[alertKey].remove();
        delete activeAlerts[alertKey];
        console.log(`✅ Cart page - Alert ${alertKey} dismissed and removed`);

        // Remove from sessionStorage
        let storedAlerts = JSON.parse(sessionStorage.getItem('persistentAlerts') || '[]');
        storedAlerts = storedAlerts.filter(alert => alert.alertKey !== alertKey);
        sessionStorage.setItem('persistentAlerts', JSON.stringify(storedAlerts));

        // Reposition remaining alerts
        repositionAlerts();
    }
}

// Function to reposition alerts after one is removed
function repositionAlerts() {
    const remainingAlerts = document.querySelectorAll('[id^="persistent-alert-"]');
    remainingAlerts.forEach((alert, index) => {
        alert.style.top = `${20 + (index * 80)}px`;
    });
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Override any existing beforeunload warnings for checkout
    window.addEventListener('beforeunload', function(e) {
        const isCheckoutNavigation = sessionStorage.getItem('isCheckoutNavigation') === 'true';
        if (isCheckoutNavigation) {
            console.log('🛒 Cart page - Checkout navigation detected - allowing without warning');
            return; // Don't show any warning
        }
    });

    // Restore persistent alerts
    restorePersistentAlerts();

    // Start monitoring for alerts (check every 3 seconds)
    checkForAlerts();
    setInterval(checkForAlerts, 3000);

    // Check and block checkout if needed
    checkCheckoutAllowed();

    // Periodically check for alert changes
    setInterval(checkCheckoutAllowed, 1000);

    // Single unified checkout click handler
    document.addEventListener('click', function(e) {
        const target = e.target.closest('a[href*="checkout"]');
        if (target) {
            console.log('🛒 Checkout button clicked - checking for active alerts...');

            if (hasActiveAlerts()) {
                // BLOCK CHECKOUT - There are active alerts
                console.log('🚫 CHECKOUT BLOCKED - Active alerts detected');
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();
                alert('🚫 Checkout Blocked!\n\nSecurity alerts detected! Please return to the scanning page and resolve all issues before proceeding to checkout.');
                return false;
            } else {
                // ALLOW CHECKOUT - No alerts, prepare smooth navigation
                console.log('✅ CHECKOUT ALLOWED - No active alerts, preparing navigation');

                // Set flag to prevent beforeunload warnings
                sessionStorage.setItem('isCheckoutNavigation', 'true');

                // Temporarily disable all beforeunload handlers
                const originalOnBeforeUnload = window.onbeforeunload;
                window.onbeforeunload = null;

                // Restore after navigation (in case it fails)
                setTimeout(() => {
                    sessionStorage.removeItem('isCheckoutNavigation');
                    window.onbeforeunload = originalOnBeforeUnload;
                }, 3000);

                console.log('🛒 Checkout navigation prepared - proceeding to checkout');
                // Allow normal navigation to continue
            }
        }
    }, true); // Use capture phase to catch early
});
</script>
{% endblock %}