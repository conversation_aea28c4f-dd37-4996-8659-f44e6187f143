{% extends "base.html" %}

{% block title %}Payment{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <!-- Order Summary Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <h3 class="card-title mb-4">
                        <i class="bi bi-receipt text-primary me-2"></i>Order Summary
                    </h3>
                    
                    <!-- Cart Items Summary -->
                    <div class="table-responsive mb-3">
                        <table class="table table-borderless">
                            <thead class="table-light">
                                <tr>
                                    <th>Item</th>
                                    <th class="text-center">Quantity</th>
                                    <th class="text-end">Price</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in session['cart'] %}
                                <tr>
                                    <td>{{ item['name'] }}</td>
                                    <td class="text-center">{{ item['quantity'] }}</td>
                                    <td class="text-end">${{ "%.2f"|format(item['price'] * item['quantity']) }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Total Calculation -->
                    <div class="border-top pt-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Subtotal:</span>
                            <strong>${{ "%.2f"|format(total) }}</strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Tax (5%):</span>
                            <strong>${{ "%.2f"|format(total * 0.05) }}</strong>
                        </div>
                        <div class="d-flex justify-content-between mt-3">
                            <strong class="fs-5">Total Due:</strong>
                            <strong class="text-primary fs-5">${{ "%.2f"|format(total * 1.05) }}</strong>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Methods Card -->
            <div class="card shadow-sm">
                <div class="card-body">
                    <h2 class="card-title text-center mb-4">
                        <i class="bi bi-credit-card text-primary"></i> Choose Payment Method
                    </h2>

                    {% if error %}
                    <div class="alert alert-danger" role="alert">
                        <i class="bi bi-exclamation-triangle-fill"></i> {{ error }}
                    </div>
                    {% endif %}

                    <div class="row g-4">
                        <!-- QR Code Payment Option -->
                        <div class="col-md-6">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <i class="bi bi-qr-code display-4 text-success mb-3"></i>
                                    <h3>QR Code Payment</h3>
                                    <p class="text-muted">Scan QR code to pay instantly</p>
                                    <a href="{{ url_for('customer.complete_payment', payment_method='qr_code') }}" 
                                       class="btn btn-success w-100">
                                        <i class="bi bi-qr-code"></i> Pay with QR Code
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Card Payment Option -->
                        <div class="col-md-6">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <i class="bi bi-credit-card display-4 text-primary mb-3"></i>
                                    <h3>Card Payment</h3>
                                    <p class="text-muted">Pay using credit or debit card</p>
                                    <a href="{{ url_for('customer.complete_payment', payment_method='card') }}" 
                                       class="btn btn-primary w-100">
                                        <i class="bi bi-credit-card"></i> Pay with Card
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Back to Cart Button -->
                    <div class="text-center mt-4">
                        <a href="{{ url_for('customer.cart') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Cart
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 