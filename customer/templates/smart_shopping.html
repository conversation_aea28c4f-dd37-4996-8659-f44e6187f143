{% extends "base.html" %}

{% block title %}Smart Shopping{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <a href="/" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Welcome
        </a>
        <h1 class="mb-0 text-center flex-grow-1">
            <i class="bi bi-camera-video"></i> Smart Shopping Cart
        </h1>
        <a href="{{ url_for('customer.cart') }}" class="btn btn-primary">
            <i class="bi bi-cart"></i> View Cart 
            <span class="badge bg-light text-dark" id="cartCount">0</span>
        </a>
    </div>

    <!-- Status Panel -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-3">
                                <i class="bi bi-shield-check"></i> Camera Status
                                <span class="badge" id="cameraStatus">
                                    <span class="status-indicator"></span>
                                    Checking...
                                </span>
                            </h5>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-success" id="startShopping">
                                    <i class="bi bi-play-fill"></i> Start Smart Shopping
                                </button>
                                <button type="button" class="btn btn-danger" id="stopShopping">
                                    <i class="bi bi-stop-fill"></i> Stop Shopping
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="text-center">
                                <h6 class="text-muted mb-2">Total Items Detected</h6>
                                <span class="display-4 text-primary" id="detectedCount">0</span>
                                <div class="mt-2">
                                    <button class="btn btn-sm btn-warning" id="testDetection">
                                        🧪 Test Alert System
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Instructions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle"></i> How Smart Monitoring Works:</h6>
                <ol class="mb-0">
                    <li>Click "Start Smart Shopping" to activate the camera monitoring</li>
                    <li>Place products in your cart - they'll be detected by the camera</li>
                    <li>Watch visual alerts for any security issues or unscanned items</li>
                    <li>Use manual scanning or add items to cart normally</li>
                    <li>The system will alert you if there are any discrepancies</li>
                </ol>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Detected Products Panel -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-eye"></i> Camera Detection
                    </h5>
                </div>
                <div class="card-body">
                    <div id="detectedProducts">
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-camera-video-off" style="font-size: 3rem;"></i>
                            <p class="mt-2">Start shopping to see detected products</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Manual Actions Panel -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-cart"></i> Shopping Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>Current Cart Status:</h6>
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Items in cart:</span>
                            <span class="badge bg-primary" id="cartItemCount">0</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <span>Cart total:</span>
                            <span class="text-success fw-bold" id="cartTotal">$0.00</span>
                        </div>
                    </div>

                    <div class="border-top pt-3">
                        <h6>Manual Actions:</h6>
                        <div class="d-grid gap-2">
                            <a href="{{ url_for('customer.index') }}" class="btn btn-outline-primary">
                                <i class="bi bi-upc-scan"></i> Manual Barcode Scan
                            </a>
                            <a href="{{ url_for('customer.cart') }}" class="btn btn-primary" id="viewCartBtn">
                                <i class="bi bi-cart"></i> View & Edit Cart
                            </a>
                            <button class="btn btn-success" id="proceedCheckout" disabled>
                                <i class="bi bi-credit-card"></i> Proceed to Checkout
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts Panel -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="bi bi-exclamation-triangle"></i> Security Alerts
                        <span class="badge bg-danger" id="alertCount">0</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div id="securityAlerts">
                        <p class="text-muted mb-0">No security alerts</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.status-online { background-color: #28a745; }
.status-offline { background-color: #dc3545; }
.status-warning { background-color: #ffc107; }

.product-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    background: white;
}

.product-item.detected {
    border-color: #007bff;
    background: #f8f9ff;
}

.product-item.in-cart {
    border-color: #28a745;
    background: #f8fff8;
}

.alert-item {
    border-left: 4px solid #dc3545;
    background: #fff5f5;
    padding: 10px;
    margin-bottom: 8px;
    border-radius: 4px;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
}

.badge.bg-primary { background-color: #007bff !important; }
.badge.bg-success { background-color: #28a745 !important; }
.badge.bg-warning { background-color: #ffc107 !important; }
</style>
{% endblock %}

{% block extra_js %}
<script>
let updateInterval;
let isShoppingActive = false;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize
    updateCartCount();
    checkCameraStatus();
    
    // Event listeners
    document.getElementById('startShopping').addEventListener('click', startShopping);
    document.getElementById('stopShopping').addEventListener('click', stopShopping);
    document.getElementById('proceedCheckout').addEventListener('click', proceedToCheckout);
    document.getElementById('testDetection').addEventListener('click', testAlertSystem);
});

function updateCartCount() {
    fetch('/customer/get-cart-count')
        .then(response => response.json())
        .then(data => {
            document.getElementById('cartCount').textContent = data.count;
        })
        .catch(error => console.error('Error updating cart count:', error));
}

function checkCameraStatus() {
    fetch('/customer/api/camera/shopping-status')
        .then(response => response.json())
        .then(data => {
            updateCameraStatus(data.camera_running);
            if (data.camera_running) {
                startDataUpdates();
            }
        })
        .catch(error => {
            console.error('Error checking camera status:', error);
            updateCameraStatus(false);
        });
}

function updateCameraStatus(isRunning) {
    const statusElement = document.getElementById('cameraStatus');
    const indicator = statusElement.querySelector('.status-indicator');
    
    if (isRunning) {
        statusElement.innerHTML = '<span class="status-indicator status-online"></span>Camera Active';
        statusElement.className = 'badge bg-success';
        isShoppingActive = true;
    } else {
        statusElement.innerHTML = '<span class="status-indicator status-offline"></span>Camera Offline';
        statusElement.className = 'badge bg-secondary';
        isShoppingActive = false;
    }
}

function startShopping() {
    fetch('/customer/api/camera/start-shopping')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateCameraStatus(true);
                startDataUpdates();
                showAlert('success', 'Smart shopping started! Place items in your cart.');
            } else {
                showAlert('danger', 'Failed to start camera: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error starting shopping:', error);
            showAlert('danger', 'Error starting smart shopping');
        });
}

function stopShopping() {
    fetch('/customer/api/camera/stop-shopping')
        .then(response => response.json())
        .then(data => {
            updateCameraStatus(false);
            stopDataUpdates();
            showAlert('info', 'Smart shopping stopped');
        })
        .catch(error => console.error('Error stopping shopping:', error));
}

function startDataUpdates() {
    if (updateInterval) return;
    updateInterval = setInterval(updateShoppingData, 2000); // Update every 2 seconds
    updateShoppingData(); // Initial update
}

function stopDataUpdates() {
    if (updateInterval) {
        clearInterval(updateInterval);
        updateInterval = null;
    }
}

function updateShoppingData() {
    if (!isShoppingActive) return;

    fetch('/customer/api/camera/detected-products')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateDetectedProducts(data.detected_products);
                updateCartDisplay(data.cart_count, data.cart_total);
                checkForAlerts(data.detected_products, data.cart_products);
            }
        })
        .catch(error => console.error('Error updating shopping data:', error));

    // Update security alerts
    fetch('/customer/api/camera/shopping-status')
        .then(response => response.json())
        .then(data => {
            updateSecurityAlerts(data.recent_alarms || []);
        })
        .catch(error => console.error('Error updating alerts:', error));
}

function checkForAlerts(detectedProducts, cartProducts) {
    // Check for unscanned products
    for (const [product, detectedCount] of Object.entries(detectedProducts)) {
        const cartCount = cartProducts[product] || 0;

        if (cartCount === 0 && detectedCount > 0) {
            showAlert('warning', `⚠️ Unscanned product detected: ${product} (${detectedCount} items)`);
        } else if (detectedCount > cartCount) {
            showAlert('warning', `⚠️ Extra items detected: ${product} - Scanned: ${cartCount}, Detected: ${detectedCount}`);
        }
    }

    // Check for removed products
    for (const [product, cartCount] of Object.entries(cartProducts)) {
        if (cartCount > 0 && !detectedProducts[product]) {
            showAlert('danger', `🚨 Product removed from cart: ${product}`);
        }
    }
}

function updateDetectedProducts(detectedProducts) {
    const container = document.getElementById('detectedProducts');
    const totalCount = Object.values(detectedProducts).reduce((sum, count) => sum + count, 0);

    document.getElementById('detectedCount').textContent = totalCount;

    if (Object.keys(detectedProducts).length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="bi bi-search" style="font-size: 3rem;"></i>
                <p class="mt-2">Looking for products...</p>
            </div>
        `;
    } else {
        let html = '';
        for (const [product, count] of Object.entries(detectedProducts)) {
            html += `
                <div class="product-item detected">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">${product}</h6>
                            <small class="text-muted">
                                <i class="bi bi-camera"></i> Detected by camera
                            </small>
                        </div>
                        <span class="badge bg-warning text-dark">${count} detected</span>
                    </div>
                </div>
            `;
        }
        container.innerHTML = html;
    }
}

function updateCartDisplay(cartCount, cartTotal) {
    document.getElementById('cartTotal').textContent = `$${cartTotal.toFixed(2)}`;
    document.getElementById('cartItemCount').textContent = cartCount;

    const checkoutBtn = document.getElementById('proceedCheckout');
    checkoutBtn.disabled = cartCount === 0;

    // Update view cart button
    const viewCartBtn = document.getElementById('viewCartBtn');
    if (cartCount > 0) {
        viewCartBtn.innerHTML = `<i class="bi bi-cart"></i> View Cart (${cartCount} items)`;
    } else {
        viewCartBtn.innerHTML = `<i class="bi bi-cart"></i> View & Edit Cart`;
    }
}

function updateSecurityAlerts(alerts) {
    const container = document.getElementById('securityAlerts');
    const alertCount = document.getElementById('alertCount');
    
    alertCount.textContent = alerts.length;
    
    if (alerts.length === 0) {
        container.innerHTML = '<p class="text-muted mb-0">No security alerts</p>';
    } else {
        let html = '';
        alerts.forEach(alert => {
            html += `
                <div class="alert-item">
                    <div class="d-flex justify-content-between">
                        <span>${alert.message}</span>
                        <small>${alert.timestamp}</small>
                    </div>
                </div>
            `;
        });
        container.innerHTML = html;
    }
}

function proceedToCheckout() {
    window.location.href = '/customer/cart';
}

function showAlert(type, message) {
    // Create and show prominent alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 400px;
        max-width: 500px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        border: 2px solid;
        font-weight: 500;
    `;

    // Add sound effect for warnings and dangers
    if (type === 'warning' || type === 'danger') {
        // You can add audio alert here if needed
        alertDiv.style.animation = 'pulse 0.5s ease-in-out 3';
    }

    alertDiv.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="me-3">
                ${type === 'warning' ? '<i class="bi bi-exclamation-triangle-fill" style="font-size: 1.5rem;"></i>' :
                  type === 'danger' ? '<i class="bi bi-shield-exclamation" style="font-size: 1.5rem;"></i>' :
                  '<i class="bi bi-info-circle-fill" style="font-size: 1.5rem;"></i>'}
            </div>
            <div class="flex-grow-1">${message}</div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    document.body.appendChild(alertDiv);

    // Auto remove after 8 seconds for warnings/dangers, 5 seconds for others
    const timeout = (type === 'warning' || type === 'danger') ? 8000 : 5000;
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, timeout);
}

// Add CSS for pulse animation
const style = document.createElement('style');
style.textContent = `
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
`;
document.head.appendChild(style);

function testAlertSystem() {
    // Test different types of alerts
    showAlert('warning', '⚠️ TEST: Unscanned product detected: Coca Cola (2 items)');

    setTimeout(() => {
        showAlert('danger', '🚨 TEST: Product removed from cart: Chips');
    }, 1000);

    setTimeout(() => {
        showAlert('warning', '⚠️ TEST: Extra items detected: Apple - Scanned: 1, Detected: 3');
    }, 2000);

    // Simulate detected products in the panel
    const container = document.getElementById('detectedProducts');
    container.innerHTML = `
        <div class="product-item detected">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">Coca Cola</h6>
                    <small class="text-muted">
                        <i class="bi bi-camera"></i> Detected by camera (TEST)
                    </small>
                </div>
                <span class="badge bg-warning text-dark">2 detected</span>
            </div>
        </div>
        <div class="product-item detected">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">Chips</h6>
                    <small class="text-muted">
                        <i class="bi bi-camera"></i> Detected by camera (TEST)
                    </small>
                </div>
                <span class="badge bg-warning text-dark">1 detected</span>
            </div>
        </div>
    `;

    document.getElementById('detectedCount').textContent = '3';

    showAlert('info', '🧪 Test alerts displayed! This is how the system will look when detecting products.');
}
</script>
{% endblock %}
