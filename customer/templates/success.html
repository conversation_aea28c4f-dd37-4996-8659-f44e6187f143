{% extends "base.html" %}

{% block title %}Payment Successful{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center p-5">
                    <div class="mb-4">
                        <i class="bi bi-check-circle-fill text-success display-1"></i>
                    </div>
                    <h1 class="mb-4">Payment Successful!</h1>
                    
                    <div class="alert alert-success mb-4">
                        <h4 class="alert-heading mb-3">
                            {% if payment_method == 'qr_code' %}
                                <i class="bi bi-qr-code me-2"></i>QR Code Payment Complete
                            {% else %}
                                <i class="bi bi-credit-card me-2"></i>Card Payment Complete
                            {% endif %}
                        </h4>
                        <p class="mb-2">Total Amount Paid: <strong>${{ "%.2f"|format(total) }}</strong></p>
                        <p class="mb-0">Payment Method: 
                            {% if payment_method == 'qr_code' %}
                                QR Code Payment
                            {% else %}
                                Credit/Debit Card
                            {% endif %}
                        </p>
                    </div>

                    {% if payment_method == 'qr_code' %}
                    <div class="alert alert-info mb-4">
                        <p class="mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            A confirmation has been sent to your device.
                        </p>
                    </div>
                    {% endif %}

                    <p class="text-muted mb-4">
                        Thank you for your purchase! Your order has been successfully processed.
                        {% if payment_method == 'qr_code' %}
                            A digital receipt has been sent to your device.
                        {% else %}
                            A receipt has been sent to your email.
                        {% endif %}
                    </p>

                    <div class="d-grid gap-3">
                        <a href="{{ url_for('customer.index') }}" class="btn btn-primary btn-lg">
                            <i class="bi bi-cart-plus me-2"></i>Continue Shopping
                        </a>
                        <a href="/" class="btn btn-outline-secondary">
                            <i class="bi bi-house me-2"></i>Back to Welcome
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 