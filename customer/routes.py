from flask import Blueprint, render_template, request, redirect, url_for, jsonify, session
import sqlite3
import os
from datetime import datetime
from camera_monitor import camera_monitor

customer_bp = Blueprint('customer', __name__, url_prefix='/customer', template_folder=os.path.join(os.path.dirname(__file__), 'templates'))

# Initialize shopping cart in session if it doesn't exist
@customer_bp.before_request
def initialize_cart():
    if 'cart' not in session:
        session['cart'] = []  # List of products
        session['total'] = 0  # Total price

# Database connection helper
def get_db_connection():
    db_path = os.environ.get('DATABASE_PATH')
    if not db_path:
        # Use relative path if environment variable is not set
        db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'products.db')
    print(f"Connecting to database at: {db_path}")  # Debug print
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    return conn

# Save order to database and update inventory
def save_order(cart, total, payment_method):
    if not cart:
        return False
        
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # Create new order record
        cursor.execute(
            'INSERT INTO orders (total_amount, payment_method) VALUES (?, ?)',
            (total, payment_method)
        )
        order_id = cursor.lastrowid
        
        # Save order items and update inventory
        for item in cart:
            cursor.execute(
                'INSERT INTO order_items (order_id, product_id, product_name, quantity, price) VALUES (?, ?, ?, ?, ?)',
                (order_id, item['id'], item['name'], item['quantity'], item['price'])
            )
            
            # Decrease stock quantity
            cursor.execute(
                'UPDATE products SET stock = stock - ? WHERE id = ?',
                (item['quantity'], item['id'])
            )
            
        conn.commit()
        return True
    except Exception as e:
        print(f"Error saving order: {str(e)}")
        conn.rollback()
        return False
    finally:
        conn.close()

@customer_bp.route('/')
def index():
    return render_template('index.html')

@customer_bp.route('/scan', methods=['POST'])
def scan_product():
    barcode = request.form.get('barcode')
    
    # Find product by barcode
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM products WHERE barcode = ?', (barcode,))
    product = cursor.fetchone()
    conn.close()
    
    if not product:
        return jsonify({'error': 'Product not found. Please check the barcode and try again.'}), 404
    
    # Verify stock availability
    if product['stock'] <= 0:
        return jsonify({'error': 'Sorry, this product is currently out of stock.'}), 400
    
    # Prepare product for cart
    cart_item = {
        'id': product['id'],
        'name': product['name'],
        'price': product['price'],
        'image_path': product['image_path'],
        'quantity': 1
    }
    
    # Check if product is already in cart
    cart = session.get('cart', [])
    product_exists = False
    
    for item in cart:
        if item['id'] == cart_item['id']:
            # Verify stock before increasing quantity
            if product['stock'] <= item['quantity']:
                return jsonify({'error': 'Sorry, there is not enough stock available for this product.'}), 400
                
            item['quantity'] += 1
            product_exists = True
            break
    
    if not product_exists:
        cart.append(cart_item)
    
    # Calculate total price
    total = sum(item['price'] * item['quantity'] for item in cart)
    
    session['cart'] = cart
    session['total'] = total
    
    return jsonify({
        'success': True,
        'product': {
            'id': product['id'],
            'name': product['name'],
            'price': product['price'],
            'image_path': product['image_path']
        },
        'cart_count': sum(item['quantity'] for item in cart),
        'cart_total': total
    })

@customer_bp.route('/cart')
def cart():
    cart = session.get('cart', [])
    total = session.get('total', 0)
    return render_template('shopping_page.html', cart=cart, total=total)

@customer_bp.route('/get-cart-count')
def get_cart_count():
    cart = session.get('cart', [])
    count = sum(item['quantity'] for item in cart)
    return jsonify({'count': count})

@customer_bp.route('/cart/remove/<int:product_id>')
def remove_from_cart(product_id):
    cart = session.get('cart', [])
    
    # Remove the product from cart
    cart = [item for item in cart if item['id'] != product_id]
    
    # Recalculate total price
    total = sum(item['price'] * item['quantity'] for item in cart)
    
    session['cart'] = cart
    session['total'] = total
    
    return redirect(url_for('customer.cart'))

@customer_bp.route('/cart/update/<int:product_id>/<action>')
def update_cart_item(product_id, action):
    cart = session.get('cart', [])
    
    # Find and update product quantity
    for item in cart:
        if item['id'] == product_id:
            if action == 'increase':
                # Check stock availability
                conn = get_db_connection()
                cursor = conn.cursor()
                cursor.execute('SELECT stock FROM products WHERE id = ?', (product_id,))
                product = cursor.fetchone()
                conn.close()
                
                if product and product['stock'] > item['quantity']:
                    item['quantity'] += 1
                
            elif action == 'decrease':
                if item['quantity'] > 1:
                    item['quantity'] -= 1
            
            break
    
    # Update cart total
    total = sum(item['price'] * item['quantity'] for item in cart)
    
    session['cart'] = cart
    session['total'] = total
    
    return redirect(url_for('customer.cart'))

@customer_bp.route('/checkout')
def checkout():
    # Check for active security alerts by comparing detected vs cart products
    detected_products = dict(camera_monitor.detected_products)
    cart = session.get('cart', [])

    # Create cart products dict for comparison
    cart_products = {}
    for item in cart:
        product_name = item['name'].lower()
        cart_products[product_name] = cart_products.get(product_name, 0) + item['quantity']

    # Check for security issues
    security_issues = []

    # Check for unscanned items (detected but not in cart)
    for product, detected_count in detected_products.items():
        cart_count = cart_products.get(product, 0)
        if cart_count == 0 and detected_count > 0:
            security_issues.append(f"Unscanned {product} detected in cart")
        elif detected_count > cart_count:
            security_issues.append(f"Extra {product} detected - scanned: {cart_count}, detected: {detected_count}")

    # Check for removed items (in cart but not detected)
    for product, cart_count in cart_products.items():
        detected_count = detected_products.get(product, 0)
        if cart_count > 0 and detected_count == 0:
            security_issues.append(f"Scanned {product} removed from cart")

    # If there are security issues, block checkout
    if security_issues:
        print(f"🚫 Checkout blocked due to security issues: {security_issues}")
        # Redirect back to cart with error message
        session['checkout_error'] = "Checkout blocked: Please resolve all security alerts before proceeding."
        return redirect(url_for('customer.cart'))

    # Proceed to payment page if no security issues
    return redirect(url_for('customer.payment'))

@customer_bp.route('/payment')
def payment():
    # Verify cart is not empty
    cart = session.get('cart', [])
    if not cart:
        return redirect(url_for('customer.index'))
    
    total = session.get('total', 0)
    return render_template('payment.html', total=total)

@customer_bp.route('/payment/complete/<payment_method>')
def complete_payment(payment_method):
    # Get current cart data
    cart = session.get('cart', [])
    total = session.get('total', 0)
    
    # Calculate total with tax
    total_with_tax = total * 1.05
    
    # Process order and save to database
    if save_order(cart, total_with_tax, payment_method):
        # Store payment method for confirmation
        session['payment_method'] = payment_method
        
        # Clear cart after successful order
        session['cart'] = []
        session['total'] = 0
        
        # Show success message
        return render_template('success.html', 
                             payment_method=payment_method,
                             total=total_with_tax)
    else:
        # Handle order processing failure
        return render_template('payment.html',
                             total=total,
                             error="Sorry, we couldn't process your order. Please try again or contact support.")

# ==================== CART MANAGEMENT ROUTES ====================

@customer_bp.route('/clear-cart', methods=['POST'])
def clear_cart():
    """Clear all items from the shopping cart"""
    try:
        # Clear the session cart
        session.pop('cart', None)
        session.pop('total', None)

        print("🗑️ Cart cleared - shopping session ended")
        return jsonify({'success': True, 'message': 'Cart cleared successfully'})
    except Exception as e:
        print(f"❌ Error clearing cart: {e}")
        return jsonify({'success': False, 'message': str(e)})

# ==================== CAMERA-INTEGRATED SHOPPING ROUTES ====================

@customer_bp.route('/smart-shopping')
def smart_shopping():
    """Smart shopping interface with camera integration"""
    return render_template('smart_shopping.html')

@customer_bp.route('/auto-start-shopping')
def auto_start_shopping():
    """Auto-start shopping with camera automatically activated"""
    return render_template('auto_start_shopping.html')

@customer_bp.route('/api/camera/start-shopping')
def start_camera_shopping():
    """Start camera for smart shopping"""
    try:
        print(f"🚀 Start camera request - Current status: {camera_monitor.is_running}")

        if camera_monitor.start_camera():
            # Clear previous scanned products for new shopping session
            camera_monitor.scanned_products.clear()
            print("✅ Camera started successfully for shopping")
            return jsonify({'success': True, 'message': 'Camera started successfully'})
        else:
            print("❌ Failed to start camera for shopping")
            return jsonify({'success': False, 'message': 'Failed to start camera. Please try again.'})

    except Exception as e:
        print(f"❌ Exception starting camera: {e}")
        return jsonify({'success': False, 'message': f'Error: {str(e)}'})

@customer_bp.route('/api/camera/stop-shopping')
def stop_camera_shopping():
    """Stop camera shopping"""
    try:
        print("🛑 Stop camera request")
        camera_monitor.stop_camera()
        print("✅ Camera stopped successfully")
        return jsonify({'success': True, 'message': 'Camera stopped'})
    except Exception as e:
        print(f"❌ Exception stopping camera: {e}")
        return jsonify({'success': False, 'message': str(e)})

@customer_bp.route('/api/camera/detected-products')
def get_detected_products():
    """Get currently detected products - ALERT ONLY MODE (no cart actions)"""
    try:
        print(f"🔍 API called - Camera running: {camera_monitor.is_running}")

        # Trigger frame processing if camera is running
        if camera_monitor.is_running:
            print("📸 Triggering frame processing...")
            camera_monitor.process_frame()

        # Get detected products from camera
        detected_products = camera_monitor.detected_products.copy()
        current_cart = session.get('cart', [])

        print(f"📊 Current detected products: {detected_products}")
        print(f"🛒 Current cart: {[item['name'] for item in current_cart]}")

        # Get current cart products for comparison (but don't modify cart)
        cart_products = {}
        for item in current_cart:
            cart_products[item['name']] = item['quantity']

        # Update camera monitor's scanned products to match current cart
        # (for alarm comparison purposes only)
        camera_monitor.scanned_products.clear()
        for item in current_cart:
            camera_monitor.scanned_products[item['name']] = item['quantity']

        return jsonify({
            'success': True,
            'detected_products': detected_products,
            'cart_products': cart_products,
            'cart_count': sum(item['quantity'] for item in current_cart),
            'cart_total': session.get('total', 0),
            'cart_updated': False  # Never update cart automatically
        })

    except Exception as e:
        print(f"❌ Error in get_detected_products: {e}")
        return jsonify({'success': False, 'message': str(e)})

@customer_bp.route('/api/camera/shopping-status')
def camera_shopping_status():
    """Get camera shopping status and data"""
    try:
        return jsonify({
            'camera_running': camera_monitor.is_running,
            'detected_products': dict(camera_monitor.detected_products),
            'recent_alarms': [
                {
                    'message': alarm['message'],
                    'type': alarm['type'],
                    'timestamp': alarm['timestamp'].strftime('%H:%M:%S')
                }
                for alarm in camera_monitor.get_recent_alarms(5)
            ]
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@customer_bp.route('/debug/products')
def debug_products():
    """Debug route to check products in database"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT id, name, barcode, price FROM products')
        products = cursor.fetchall()
        conn.close()

        yolo_products = ['chipsy', 'cocacola zero', 'pasta', 'shower gel', 'water']

        result = {
            'database_products': [
                {
                    'id': p[0],
                    'name': p[1],
                    'barcode': p[2],
                    'price': p[3]
                } for p in products
            ],
            'yolo_products': yolo_products,
            'name_analysis': []
        }

        # Check for name matches
        for yolo_name in yolo_products:
            matches = []
            for p in products:
                db_name = p[1].lower()
                if yolo_name.lower() in db_name or db_name in yolo_name.lower():
                    matches.append(p[1])

            result['name_analysis'].append({
                'yolo_name': yolo_name,
                'matches': matches,
                'has_match': len(matches) > 0
            })

        return jsonify(result)

    except Exception as e:
        return jsonify({'error': str(e)})
